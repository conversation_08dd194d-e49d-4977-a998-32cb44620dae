package com.ruoyi.system.domain.dto;

import javax.validation.constraints.NotNull;

/**
 * 抽奖请求DTO
 * 
 * <AUTHOR>
 */
public class DrawLotteryRequest
{
    /** 活动ID */
    @NotNull(message = "活动ID不能为空")
    private Long activityId;
    
    /** 用户OpenID */
    @NotNull(message = "用户OpenID不能为空")
    private String userOpenid;
    
    /** 用户昵称 */
    private String userNickname;
    
    /** 用户头像 */
    private String userAvatar;
    
    /** 桌台ID */
    private Long tableId;

    public Long getActivityId()
    {
        return activityId;
    }

    public void setActivityId(Long activityId)
    {
        this.activityId = activityId;
    }

    public String getUserOpenid()
    {
        return userOpenid;
    }

    public void setUserOpenid(String userOpenid)
    {
        this.userOpenid = userOpenid;
    }

    public String getUserNickname()
    {
        return userNickname;
    }

    public void setUserNickname(String userNickname)
    {
        this.userNickname = userNickname;
    }

    public String getUserAvatar()
    {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar)
    {
        this.userAvatar = userAvatar;
    }

    public Long getTableId()
    {
        return tableId;
    }

    public void setTableId(Long tableId)
    {
        this.tableId = tableId;
    }

    @Override
    public String toString()
    {
        return "DrawLotteryRequest{" +
                "activityId=" + activityId +
                ", userOpenid='" + userOpenid + '\'' +
                ", userNickname='" + userNickname + '\'' +
                ", userAvatar='" + userAvatar + '\'' +
                ", tableId=" + tableId +
                '}';
    }
}
