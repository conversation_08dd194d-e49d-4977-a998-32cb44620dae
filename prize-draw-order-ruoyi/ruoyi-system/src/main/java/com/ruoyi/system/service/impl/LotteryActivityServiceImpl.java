package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.LotteryActivity;
import com.ruoyi.system.mapper.LotteryActivityMapper;
import com.ruoyi.system.mapper.LotteryRecordMapper;
import com.ruoyi.system.service.ILotteryActivityService;

/**
 * 抽奖活动管理 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class LotteryActivityServiceImpl implements ILotteryActivityService
{
    @Autowired
    private LotteryActivityMapper lotteryActivityMapper;

    @Autowired
    private LotteryRecordMapper lotteryRecordMapper;

    /**
     * 查询抽奖活动信息
     * 
     * @param activityId 活动ID
     * @return 活动信息
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public LotteryActivity selectLotteryActivityById(Long activityId)
    {
        return lotteryActivityMapper.selectLotteryActivityById(activityId);
    }

    /**
     * 查询抽奖活动列表
     * 
     * @param lotteryActivity 活动信息
     * @return 活动集合
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<LotteryActivity> selectLotteryActivityList(LotteryActivity lotteryActivity)
    {
        return lotteryActivityMapper.selectLotteryActivityList(lotteryActivity);
    }

    /**
     * 根据商家ID查询抽奖活动列表
     * 
     * @param merchantId 商家ID
     * @return 活动集合
     */
    @Override
    public List<LotteryActivity> selectLotteryActivitiesByMerchantId(Long merchantId)
    {
        return lotteryActivityMapper.selectLotteryActivitiesByMerchantId(merchantId);
    }

    /**
     * 查询有效的抽奖活动列表（状态正常且在活动时间内）
     * 
     * @param merchantId 商家ID
     * @return 活动集合
     */
    @Override
    public List<LotteryActivity> selectValidLotteryActivities(Long merchantId)
    {
        return lotteryActivityMapper.selectValidLotteryActivities(merchantId);
    }

    /**
     * 根据商家ID查询当前有效的抽奖活动
     * 
     * @param merchantId 商家ID
     * @return 活动信息
     */
    @Override
    public LotteryActivity selectCurrentValidActivity(Long merchantId)
    {
        return lotteryActivityMapper.selectCurrentValidActivity(merchantId);
    }

    /**
     * 新增抽奖活动
     * 
     * @param lotteryActivity 活动信息
     * @return 结果
     */
    @Override
    public int insertLotteryActivity(LotteryActivity lotteryActivity)
    {
        lotteryActivity.setCreateTime(DateUtils.getNowDate());
        return lotteryActivityMapper.insertLotteryActivity(lotteryActivity);
    }

    /**
     * 修改抽奖活动
     * 
     * @param lotteryActivity 活动信息
     * @return 结果
     */
    @Override
    public int updateLotteryActivity(LotteryActivity lotteryActivity)
    {
        lotteryActivity.setUpdateTime(DateUtils.getNowDate());
        return lotteryActivityMapper.updateLotteryActivity(lotteryActivity);
    }

    /**
     * 批量删除抽奖活动
     * 
     * @param activityIds 需要删除的活动ID
     * @return 结果
     */
    @Override
    public int deleteLotteryActivityByIds(Long[] activityIds)
    {
        return lotteryActivityMapper.deleteLotteryActivityByIds(activityIds);
    }

    /**
     * 删除抽奖活动信息
     * 
     * @param activityId 活动ID
     * @return 结果
     */
    @Override
    public int deleteLotteryActivityById(Long activityId)
    {
        return lotteryActivityMapper.deleteLotteryActivityById(activityId);
    }

    /**
     * 根据商家ID删除抽奖活动
     * 
     * @param merchantId 商家ID
     * @return 结果
     */
    @Override
    public int deleteLotteryActivityByMerchantId(Long merchantId)
    {
        return lotteryActivityMapper.deleteLotteryActivityByMerchantId(merchantId);
    }

    /**
     * 统计商家的活动数量
     * 
     * @param merchantId 商家ID
     * @return 活动数量
     */
    @Override
    public int countActivitiesByMerchantId(Long merchantId)
    {
        return lotteryActivityMapper.countActivitiesByMerchantId(merchantId);
    }

    /**
     * 查询即将结束的活动列表
     * 
     * @param hours 提前小时数
     * @return 活动集合
     */
    @Override
    public List<LotteryActivity> selectEndingSoonActivities(int hours)
    {
        return lotteryActivityMapper.selectEndingSoonActivities(hours);
    }

    /**
     * 查询已过期但状态未更新的活动列表
     * 
     * @return 活动集合
     */
    @Override
    public List<LotteryActivity> selectExpiredActivities()
    {
        return lotteryActivityMapper.selectExpiredActivities();
    }

    /**
     * 更新过期活动状态
     * 
     * @return 结果
     */
    @Override
    public int updateExpiredActivitiesStatus()
    {
        return lotteryActivityMapper.updateExpiredActivitiesStatus();
    }

    /**
     * 检查活动是否有效
     * 
     * @param activityId 活动ID
     * @return 是否有效
     */
    @Override
    public boolean isActivityValid(Long activityId)
    {
        LotteryActivity activity = lotteryActivityMapper.selectLotteryActivityById(activityId);
        if (activity == null)
        {
            return false;
        }
        
        // 检查状态
        if (!"0".equals(activity.getStatus()))
        {
            return false;
        }
        
        // 检查时间
        Date now = new Date();
        if (activity.getStartTime() != null && activity.getStartTime().after(now))
        {
            return false;
        }
        
        if (activity.getEndTime() != null && activity.getEndTime().before(now))
        {
            return false;
        }
        
        return true;
    }

    /**
     * 检查用户是否可以参与抽奖
     *
     * @param activityId 活动ID
     * @param userOpenid 用户OpenID
     * @return 是否可以参与
     */
    @Override
    public boolean canUserParticipate(Long activityId, String userOpenid)
    {
        // 检查活动是否有效
        if (!isActivityValid(activityId))
        {
            return false;
        }

        // 获取活动信息
        LotteryActivity activity = lotteryActivityMapper.selectLotteryActivityById(activityId);
        if (activity == null)
        {
            return false;
        }

        // 检查每日限制
        if (activity.getDailyLimit() != null && activity.getDailyLimit() > 0)
        {
            int todayDraws = lotteryRecordMapper.countUserTodayDraws(activityId, userOpenid);
            if (todayDraws >= activity.getDailyLimit())
            {
                return false;
            }
        }

        // 检查总限制
        if (activity.getTotalLimit() != null && activity.getTotalLimit() > 0)
        {
            int totalDraws = lotteryRecordMapper.countUserTotalDraws(activityId, userOpenid);
            if (totalDraws >= activity.getTotalLimit())
            {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取用户剩余抽奖次数
     *
     * @param activityId 活动ID
     * @param userOpenid 用户OpenID
     * @return 剩余次数
     */
    @Override
    public int getUserRemainingDraws(Long activityId, String userOpenid)
    {
        LotteryActivity activity = lotteryActivityMapper.selectLotteryActivityById(activityId);
        if (activity == null)
        {
            return 0;
        }

        // 检查活动是否有效
        if (!isActivityValid(activityId))
        {
            return 0;
        }

        int remainingDraws = 0;

        // 根据每日限制计算剩余次数
        if (activity.getDailyLimit() != null && activity.getDailyLimit() > 0)
        {
            int todayDraws = lotteryRecordMapper.countUserTodayDraws(activityId, userOpenid);
            int dailyRemaining = activity.getDailyLimit() - todayDraws;
            remainingDraws = Math.max(0, dailyRemaining);
        }
        else
        {
            // 如果没有每日限制，设置一个较大的值
            remainingDraws = Integer.MAX_VALUE;
        }

        // 根据总限制进一步限制剩余次数
        if (activity.getTotalLimit() != null && activity.getTotalLimit() > 0)
        {
            int totalDraws = lotteryRecordMapper.countUserTotalDraws(activityId, userOpenid);
            int totalRemaining = activity.getTotalLimit() - totalDraws;
            remainingDraws = Math.min(remainingDraws, Math.max(0, totalRemaining));
        }

        // 如果remainingDraws还是Integer.MAX_VALUE，说明没有任何限制，返回1表示可以抽奖
        if (remainingDraws == Integer.MAX_VALUE)
        {
            remainingDraws = 1;
        }

        return remainingDraws;
    }
}
