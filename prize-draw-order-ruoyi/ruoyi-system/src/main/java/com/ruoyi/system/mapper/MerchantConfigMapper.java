package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.MerchantConfig;
import org.apache.ibatis.annotations.Param;

/**
 * 商家配置管理 数据层
 * 
 * <AUTHOR>
 */
public interface MerchantConfigMapper
{
    /**
     * 查询配置信息
     * 
     * @param configId 配置ID
     * @return 配置信息
     */
    public MerchantConfig selectMerchantConfigById(Long configId);

    /**
     * 查询配置列表
     * 
     * @param merchantConfig 配置信息
     * @return 配置集合
     */
    public List<MerchantConfig> selectMerchantConfigList(MerchantConfig merchantConfig);

    /**
     * 根据商家ID查询配置列表
     * 
     * @param merchantId 商家ID
     * @return 配置集合
     */
    public List<MerchantConfig> selectMerchantConfigsByMerchantId(Long merchantId);

    /**
     * 根据商家ID和配置键查询配置信息
     * 
     * @param merchantId 商家ID
     * @param configKey 配置键
     * @return 配置信息
     */
    public MerchantConfig selectMerchantConfigByKey(@Param("merchantId") Long merchantId, @Param("configKey")String configKey);

    /**
     * 校验配置键在商家内是否唯一
     * 
     * @param merchantConfig 配置信息
     * @return 配置信息
     */
    public MerchantConfig checkConfigKeyUnique(MerchantConfig merchantConfig);

    /**
     * 新增配置
     * 
     * @param merchantConfig 配置信息
     * @return 结果
     */
    public int insertMerchantConfig(MerchantConfig merchantConfig);

    /**
     * 修改配置
     * 
     * @param merchantConfig 配置信息
     * @return 结果
     */
    public int updateMerchantConfig(MerchantConfig merchantConfig);

    /**
     * 删除配置
     * 
     * @param configId 配置ID
     * @return 结果
     */
    public int deleteMerchantConfigById(Long configId);

    /**
     * 批量删除配置
     * 
     * @param configIds 需要删除的配置ID
     * @return 结果
     */
    public int deleteMerchantConfigByIds(Long[] configIds);

    /**
     * 根据商家ID删除配置
     * 
     * @param merchantId 商家ID
     * @return 结果
     */
    public int deleteMerchantConfigByMerchantId(Long merchantId);

    /**
     * 批量插入或更新配置
     * 
     * @param configs 配置列表
     * @return 结果
     */
    public int batchInsertOrUpdateMerchantConfig(List<MerchantConfig> configs);
}
