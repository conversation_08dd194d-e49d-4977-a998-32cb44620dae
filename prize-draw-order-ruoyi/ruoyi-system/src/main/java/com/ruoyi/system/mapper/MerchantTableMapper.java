package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.MerchantTable;
import org.apache.ibatis.annotations.Param;

/**
 * 商家桌台管理 数据层
 * 
 * <AUTHOR>
 */
public interface MerchantTableMapper
{
    /**
     * 查询桌台信息
     * 
     * @param tableId 桌台ID
     * @return 桌台信息
     */
    public MerchantTable selectMerchantTableById(Long tableId);

    /**
     * 查询桌台列表
     * 
     * @param merchantTable 桌台信息
     * @return 桌台集合
     */
    public List<MerchantTable> selectMerchantTableList(MerchantTable merchantTable);

    /**
     * 根据商家ID查询桌台列表
     * 
     * @param merchantId 商家ID
     * @return 桌台集合
     */
    public List<MerchantTable> selectMerchantTablesByMerchantId(Long merchantId);

    /**
     * 根据商家ID和桌台号查询桌台信息
     * 
     * @param merchantId 商家ID
     * @param tableNumber 桌台号
     * @return 桌台信息
     */
    public MerchantTable selectMerchantTableByMerchantIdAndNumber(@Param("merchantId") Long merchantId, @Param("tableNumber") String tableNumber);

    /**
     * 校验桌台号在商家内是否唯一
     * 
     * @param merchantTable 桌台信息
     * @return 桌台信息
     */
    public MerchantTable checkTableNumberUnique(MerchantTable merchantTable);

    /**
     * 新增桌台
     * 
     * @param merchantTable 桌台信息
     * @return 结果
     */
    public int insertMerchantTable(MerchantTable merchantTable);

    /**
     * 修改桌台
     * 
     * @param merchantTable 桌台信息
     * @return 结果
     */
    public int updateMerchantTable(MerchantTable merchantTable);

    /**
     * 删除桌台
     * 
     * @param tableId 桌台ID
     * @return 结果
     */
    public int deleteMerchantTableById(Long tableId);

    /**
     * 批量删除桌台
     * 
     * @param tableIds 需要删除的桌台ID
     * @return 结果
     */
    public int deleteMerchantTableByIds(Long[] tableIds);

    /**
     * 根据商家ID删除桌台
     * 
     * @param merchantId 商家ID
     * @return 结果
     */
    public int deleteMerchantTableByMerchantId(Long merchantId);

    /**
     * 统计商家的桌台数量
     * 
     * @param merchantId 商家ID
     * @return 桌台数量
     */
    public int countTablesByMerchantId(Long merchantId);
}
