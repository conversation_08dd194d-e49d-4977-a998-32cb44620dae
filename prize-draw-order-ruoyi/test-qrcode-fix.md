# 桌台二维码图片显示问题修复

## 问题描述
桌台管理页面的二维码图片无法正常显示，获取不到后端的图片资源。

## 问题原因分析

### 1. 路径配置问题
- **前端配置**: `VUE_APP_BASE_API = '/dev-api'`
- **后端静态资源前缀**: `RESOURCE_PREFIX = "/profile"`
- **文件上传路径**: `/Users/<USER>/projects/prize-draw-order/file`

### 2. 路径拼接错误
- **二维码生成路径**: `/upload/qrcode/filename.png`
- **前端错误拼接**: `/dev-api/upload/qrcode/filename.png`
- **正确访问路径**: `/profile/upload/qrcode/filename.png`

### 3. 代理配置缺失
- vue.config.js 中只配置了 `/dev-api` 的代理
- 缺少 `/profile` 静态资源的代理配置

## 修复方案

### 1. 修改 ImagePreview 组件
文件: `prize-draw-order-ruoyi/ruoyi-ui/src/components/ImagePreview/index.vue`

**修改内容**:
- 在 `realSrc` 计算属性中添加对 `/upload` 路径的特殊处理
- 在 `realSrcList` 计算属性中添加相同的处理逻辑
- 对以 `/upload` 开头的路径使用 `/profile` 前缀

```javascript
// 修改前
return process.env.VUE_APP_BASE_API + real_src

// 修改后
if (real_src.startsWith('/upload')) {
  return '/profile' + real_src
}
return process.env.VUE_APP_BASE_API + real_src
```

### 2. 添加静态资源代理配置
文件: `prize-draw-order-ruoyi/ruoyi-ui/vue.config.js`

**修改内容**:
- 在 devServer.proxy 中添加 `/profile` 路径的代理配置

```javascript
// 静态资源代理
"^/profile/(.*)": {
  target: baseUrl,
  changeOrigin: true,
},
```

## 修复效果

### 修复前
- 二维码图片路径: `/dev-api/upload/qrcode/filename.png`
- 访问结果: 404 Not Found

### 修复后
- 二维码图片路径: `/profile/upload/qrcode/filename.png`
- 访问结果: 正常显示图片

## 测试验证

### 1. 功能测试
1. 启动前端开发服务器: `npm run dev`
2. 访问桌台管理页面
3. 点击"生成二维码"按钮
4. 验证二维码图片是否正常显示

### 2. 路径测试
可以通过访问测试页面验证不同路径的访问效果:
- 测试文件: `prize-draw-order-ruoyi/ruoyi-ui/test-qrcode.html`
- 访问地址: `http://localhost/test-qrcode.html`

### 3. 网络请求验证
在浏览器开发者工具中检查:
- Network 标签页中的图片请求
- 确认请求路径为 `/profile/upload/qrcode/filename.png`
- 确认响应状态为 200 OK

## 相关文件说明

### 后端配置
- **静态资源配置**: `ResourcesConfig.java`
- **文件上传配置**: `application.yml`
- **二维码生成**: `QrCodeUtils.java`

### 前端配置
- **环境变量**: `.env.development`
- **代理配置**: `vue.config.js`
- **图片组件**: `ImagePreview/index.vue`

## 注意事项

1. **开发环境**: 修改后需要重启前端开发服务器
2. **生产环境**: 需要确保 Nginx 配置正确代理 `/profile` 路径
3. **文件权限**: 确保上传目录有正确的读写权限
4. **路径一致性**: 前后端的路径配置必须保持一致

## 后续优化建议

1. **统一路径管理**: 考虑将静态资源路径配置统一管理
2. **错误处理**: 添加图片加载失败的友好提示
3. **缓存策略**: 为静态资源配置合适的缓存策略
4. **安全考虑**: 限制静态资源的访问权限和文件类型
