-- 商家权限配置SQL脚本

-- 创建商家角色
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES 
(100, '商家管理员', 'merchant_admin', 3, '5', 1, 1, '0', '0', 'admin', sysdate(), '商家管理员角色，只能管理自己商家的数据');

-- 创建商家用户表
CREATE TABLE IF NOT EXISTS `merchant_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_merchant_user_name` (`user_name`),
  KEY `idx_merchant_id` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家用户信息表';

-- 商家菜单权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
-- 商家管理主菜单
(2000, '商家管理', 0, 5, 'merchant', NULL, '', 1, 0, 'M', '0', '0', '', 'merchant', 'admin', sysdate(), '商家管理目录'),

-- 超级管理员的商家列表管理
(2001, '商家列表', 2000, 1, 'list', 'system/merchant/index', '', 1, 0, 'C', '0', '0', 'merchant:info:list', 'list', 'admin', sysdate(), '商家列表菜单'),
(2002, '商家查询', 2001, 1, '', '', '', 1, 0, 'F', '0', '0', 'merchant:info:query', '#', 'admin', sysdate(), ''),
(2003, '商家新增', 2001, 2, '', '', '', 1, 0, 'F', '0', '0', 'merchant:info:add', '#', 'admin', sysdate(), ''),
(2004, '商家修改', 2001, 3, '', '', '', 1, 0, 'F', '0', '0', 'merchant:info:edit', '#', 'admin', sysdate(), ''),
(2005, '商家删除', 2001, 4, '', '', '', 1, 0, 'F', '0', '0', 'merchant:info:remove', '#', 'admin', sysdate(), ''),
(2006, '商家导出', 2001, 5, '', '', '', 1, 0, 'F', '0', '0', 'merchant:info:export', '#', 'admin', sysdate(), ''),

-- 商家信息管理（商家用户使用）
(2010, '商家信息', 2000, 2, 'info', 'merchant/info/index', '', 1, 0, 'C', '0', '0', 'merchant:info:list', 'user', 'admin', sysdate(), '商家信息菜单'),
(2011, '商家信息查询', 2010, 1, '', '', '', 1, 0, 'F', '0', '0', 'merchant:info:query', '#', 'admin', sysdate(), ''),
(2012, '商家信息修改', 2010, 2, '', '', '', 1, 0, 'F', '0', '0', 'merchant:info:edit', '#', 'admin', sysdate(), ''),

-- 桌台管理
(2020, '桌台管理', 2000, 3, 'table', 'merchant/table/index', '', 1, 0, 'C', '0', '0', 'merchant:table:list', 'table', 'admin', sysdate(), '桌台管理菜单'),
(2021, '桌台查询', 2020, 1, '', '', '', 1, 0, 'F', '0', '0', 'merchant:table:query', '#', 'admin', sysdate(), ''),
(2022, '桌台新增', 2020, 2, '', '', '', 1, 0, 'F', '0', '0', 'merchant:table:add', '#', 'admin', sysdate(), ''),
(2023, '桌台修改', 2020, 3, '', '', '', 1, 0, 'F', '0', '0', 'merchant:table:edit', '#', 'admin', sysdate(), ''),
(2024, '桌台删除', 2020, 4, '', '', '', 1, 0, 'F', '0', '0', 'merchant:table:remove', '#', 'admin', sysdate(), ''),
(2025, '桌台导出', 2020, 5, '', '', '', 1, 0, 'F', '0', '0', 'merchant:table:export', '#', 'admin', sysdate(), ''),

-- 配置管理
(2030, '配置管理', 2000, 4, 'config', 'merchant/config/index', '', 1, 0, 'C', '0', '0', 'merchant:config:list', 'config', 'admin', sysdate(), '配置管理菜单'),
(2031, '配置查询', 2030, 1, '', '', '', 1, 0, 'F', '0', '0', 'merchant:config:query', '#', 'admin', sysdate(), ''),
(2032, '配置新增', 2030, 2, '', '', '', 1, 0, 'F', '0', '0', 'merchant:config:add', '#', 'admin', sysdate(), ''),
(2033, '配置修改', 2030, 3, '', '', '', 1, 0, 'F', '0', '0', 'merchant:config:edit', '#', 'admin', sysdate(), ''),
(2034, '配置删除', 2030, 4, '', '', '', 1, 0, 'F', '0', '0', 'merchant:config:remove', '#', 'admin', sysdate(), ''),

-- 抽奖管理主菜单
(2100, '抽奖管理', 0, 6, 'lottery', NULL, '', 1, 0, 'M', '0', '0', '', 'lottery', 'admin', sysdate(), '抽奖管理目录'),

-- 抽奖活动管理
(2101, '抽奖活动', 2100, 1, 'activity', 'lottery/activity/index', '', 1, 0, 'C', '0', '0', 'lottery:activity:list', 'activity', 'admin', sysdate(), '抽奖活动菜单'),
(2102, '活动查询', 2101, 1, '', '', '', 1, 0, 'F', '0', '0', 'lottery:activity:query', '#', 'admin', sysdate(), ''),
(2103, '活动新增', 2101, 2, '', '', '', 1, 0, 'F', '0', '0', 'lottery:activity:add', '#', 'admin', sysdate(), ''),
(2104, '活动修改', 2101, 3, '', '', '', 1, 0, 'F', '0', '0', 'lottery:activity:edit', '#', 'admin', sysdate(), ''),
(2105, '活动删除', 2101, 4, '', '', '', 1, 0, 'F', '0', '0', 'lottery:activity:remove', '#', 'admin', sysdate(), ''),
(2106, '活动导出', 2101, 5, '', '', '', 1, 0, 'F', '0', '0', 'lottery:activity:export', '#', 'admin', sysdate(), ''),

-- 抽奖记录管理
(2110, '抽奖记录', 2100, 2, 'record', 'lottery/record/index', '', 1, 0, 'C', '0', '0', 'lottery:record:list', 'record', 'admin', sysdate(), '抽奖记录菜单'),
(2111, '记录查询', 2110, 1, '', '', '', 1, 0, 'F', '0', '0', 'lottery:record:query', '#', 'admin', sysdate(), ''),
(2112, '记录修改', 2110, 2, '', '', '', 1, 0, 'F', '0', '0', 'lottery:record:edit', '#', 'admin', sysdate(), ''),
(2113, '记录删除', 2110, 3, '', '', '', 1, 0, 'F', '0', '0', 'lottery:record:remove', '#', 'admin', sysdate(), ''),
(2114, '记录导出', 2110, 4, '', '', '', 1, 0, 'F', '0', '0', 'lottery:record:export', '#', 'admin', sysdate(), ''),

-- 数据统计
(2120, '数据统计', 2100, 3, 'statistics', 'lottery/statistics/index', '', 1, 0, 'C', '0', '0', 'lottery:statistics:view', 'chart', 'admin', sysdate(), '数据统计菜单');

-- 为超级管理员角色分配商家管理权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 超级管理员商家管理权限
(1, 2000), (1, 2001), (1, 2002), (1, 2003), (1, 2004), (1, 2005), (1, 2006);

-- 为商家角色分配菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 商家管理权限
(100, 2000), (100, 2010), (100, 2011), (100, 2012),
(100, 2020), (100, 2021), (100, 2022), (100, 2023), (100, 2024), (100, 2025),
(100, 2030), (100, 2031), (100, 2032), (100, 2033), (100, 2034),
-- 抽奖管理权限
(100, 2100), (100, 2101), (100, 2102), (100, 2103), (100, 2104), (100, 2105), (100, 2106),
(100, 2110), (100, 2111), (100, 2112), (100, 2113), (100, 2114),
(100, 2120);

-- 创建商家用户与系统用户的关联表
CREATE TABLE IF NOT EXISTS `merchant_user_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `sys_user_id` bigint(20) NOT NULL COMMENT '系统用户ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_merchant_sys_user` (`merchant_id`, `sys_user_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_sys_user_id` (`sys_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家用户关联表';

-- 创建数据权限过滤器配置
INSERT INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, remark) VALUES 
(100, '商家数据权限过滤', 'merchant.data.scope.filter', 'true', 'Y', 'admin', sysdate(), '是否启用商家数据权限过滤');

-- 创建示例商家用户（密码：123456）
INSERT INTO sys_user (user_id, user_name, nick_name, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, remark) VALUES 
(100, 'merchant001', '商家001', '<EMAIL>', '13800000001', '0', '', '$2a$10$7JB720yubVSOfvVMe6/YqOPKYvuFI5wHrvi0QIDQ/.p.uROlFfmHa', '0', '0', '', NULL, 'admin', sysdate(), '示例商家用户');

-- 为商家用户分配角色
INSERT INTO sys_user_role (user_id, role_id) VALUES (100, 100);

-- 创建商家用户关联
INSERT INTO merchant_user_relation (merchant_id, sys_user_id, create_time) VALUES (1, 100, sysdate());

COMMIT;
