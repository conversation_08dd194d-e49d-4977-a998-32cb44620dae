-- 抽奖功能优化：添加唯一约束防止重复抽奖
-- 执行时间：2025-01-31
-- 说明：为lottery_record表添加唯一约束，确保同一用户在同一活动中只能抽奖一次

-- 1. 检查是否已存在重复数据
SELECT 
    activity_id, 
    user_openid, 
    COUNT(*) as count
FROM lottery_record 
WHERE user_openid IS NOT NULL 
GROUP BY activity_id, user_openid 
HAVING COUNT(*) > 1;

-- 2. 如果存在重复数据，需要先清理（保留最早的记录）
-- 注意：在生产环境中执行前请先备份数据
/*
DELETE lr1 FROM lottery_record lr1
INNER JOIN lottery_record lr2 
WHERE lr1.activity_id = lr2.activity_id 
  AND lr1.user_openid = lr2.user_openid 
  AND lr1.record_id > lr2.record_id;
*/

-- 3. 添加唯一约束，防止同一用户在同一活动中重复抽奖
ALTER TABLE lottery_record 
ADD CONSTRAINT uk_activity_user 
UNIQUE KEY (activity_id, user_openid);

-- 4. 验证约束是否添加成功
SHOW INDEX FROM lottery_record WHERE Key_name = 'uk_activity_user';

-- 5. 测试约束是否生效（这个插入应该会失败，如果已存在相同的activity_id和user_openid）
/*
-- 测试插入重复数据（应该失败）
INSERT INTO lottery_record (activity_id, merchant_id, user_openid, user_nickname, prize_name, prize_type, is_winner, claim_status, draw_time, create_time)
VALUES (1, 1, 'test_user_001', '测试用户', '谢谢参与', 'none', '0', '0', NOW(), NOW());

-- 再次插入相同用户和活动（应该失败）
INSERT INTO lottery_record (activity_id, merchant_id, user_openid, user_nickname, prize_name, prize_type, is_winner, claim_status, draw_time, create_time)
VALUES (1, 1, 'test_user_001', '测试用户', '一等奖', 'prize', '1', '0', NOW(), NOW());
*/

-- 6. 如果需要回滚约束（仅在测试环境使用）
/*
ALTER TABLE lottery_record DROP INDEX uk_activity_user;
*/
