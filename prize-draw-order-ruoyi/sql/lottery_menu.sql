-- 抽奖管理菜单 SQL
-- 一级菜单：抽奖管理
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖管理', '0', '5', 'lottery', null, '1', '0', 'M', '0', '0', '', 'star', 'admin', sysdate(), '', null, '抽奖管理目录');

-- 获取刚插入的一级菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 二级菜单：抽奖活动管理
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖活动', @parentId, '1', 'activity', 'lottery/activity/index', '1', '0', 'C', '0', '0', 'lottery:activity:list', 'list', 'admin', sysdate(), '', null, '抽奖活动菜单');

-- 获取抽奖活动菜单ID
SELECT @activityMenuId := LAST_INSERT_ID();

-- 抽奖活动按钮权限
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖活动查询', @activityMenuId, '1', '#', '', '1', '0', 'F', '0', '0', 'lottery:activity:query', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖活动新增', @activityMenuId, '2', '#', '', '1', '0', 'F', '0', '0', 'lottery:activity:add', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖活动修改', @activityMenuId, '3', '#', '', '1', '0', 'F', '0', '0', 'lottery:activity:edit', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖活动删除', @activityMenuId, '4', '#', '', '1', '0', 'F', '0', '0', 'lottery:activity:remove', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖活动导出', @activityMenuId, '5', '#', '', '1', '0', 'F', '0', '0', 'lottery:activity:export', '#', 'admin', sysdate(), '', null, '');

-- 二级菜单：抽奖记录管理
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖记录', @parentId, '2', 'record', 'lottery/record/index', '1', '0', 'C', '0', '0', 'lottery:record:list', 'documentation', 'admin', sysdate(), '', null, '抽奖记录菜单');

-- 获取抽奖记录菜单ID
SELECT @recordMenuId := LAST_INSERT_ID();

-- 抽奖记录按钮权限
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖记录查询', @recordMenuId, '1', '#', '', '1', '0', 'F', '0', '0', 'lottery:record:query', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖记录修改', @recordMenuId, '2', '#', '', '1', '0', 'F', '0', '0', 'lottery:record:edit', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖记录删除', @recordMenuId, '3', '#', '', '1', '0', 'F', '0', '0', 'lottery:record:remove', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('抽奖记录导出', @recordMenuId, '4', '#', '', '1', '0', 'F', '0', '0', 'lottery:record:export', '#', 'admin', sysdate(), '', null, '');

-- 为管理员角色分配抽奖管理权限
-- 获取管理员角色ID（通常是1）
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE menu_name IN ('抽奖管理', '抽奖活动', '抽奖记录') 
   OR perms LIKE 'lottery:%';

-- 为商家角色分配抽奖管理权限（如果存在商家角色）
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT role_id, menu_id FROM sys_role, sys_menu 
WHERE role_key = 'merchant' AND (menu_name IN ('抽奖管理', '抽奖活动', '抽奖记录') OR perms LIKE 'lottery:%');
