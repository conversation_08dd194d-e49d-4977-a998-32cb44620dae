# 抽奖接口参数获取方式修复

## 问题描述

原来的抽奖接口 `/api/lottery/draw` 使用 `@RequestParam` 注解来接收参数，但前端发送的是JSON格式的请求体数据，导致参数无法正确获取。

## 修复内容

### 1. 创建DTO类

创建了 `DrawLotteryRequest.java` DTO类来封装抽奖请求参数：

```java
public class DrawLotteryRequest {
    @NotNull(message = "活动ID不能为空")
    private Long activityId;
    
    @NotNull(message = "用户OpenID不能为空")
    private String userOpenid;
    
    private String userNickname;
    private String userAvatar;
    private Long tableId;
    
    // getter/setter方法...
}
```

### 2. 修改Controller方法

#### API控制器 (ApiLotteryController.java)

**修改前：**
```java
@PostMapping("/draw")
public AjaxResult performDraw(@RequestParam Long activityId,
                            @RequestParam String userOpenid,
                            @RequestParam(required = false) String userNickname,
                            @RequestParam(required = false) String userAvatar,
                            @RequestParam(required = false) Long tableId,
                            HttpServletRequest request)
```

**修改后：**
```java
@PostMapping("/draw")
public AjaxResult performDraw(@Valid @RequestBody DrawLotteryRequest request,
                            HttpServletRequest httpRequest)
```

#### 系统管理控制器 (LotteryRecordController.java)

同样的修改方式，使用 `@RequestBody` 和 DTO 来接收JSON参数。

### 3. 前端调用方式

前端调用方式保持不变，继续使用JSON格式发送数据：

```javascript
const drawData = {
  activityId: this.currentActivity.activityId,
  userOpenid: this.userOpenid,
  userNickname: '用户' + this.userOpenid.slice(-4),
  userAvatar: '',
  tableId: null
}

const res = await lotteryApi.performDraw(drawData)
```

## 修复的优势

1. **正确处理JSON数据**：使用 `@RequestBody` 可以正确解析前端发送的JSON格式数据
2. **参数验证**：通过 `@Valid` 注解和DTO中的验证注解，可以自动进行参数校验
3. **代码更清晰**：使用DTO封装参数，代码结构更清晰，易于维护
4. **类型安全**：DTO提供了类型安全的参数传递方式

## 测试方法

### 1. 启动后端服务

```bash
cd prize-draw-order-ruoyi
mvn clean install -DskipTests
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

### 2. 测试API接口

使用Postman或curl测试抽奖接口：

```bash
curl -X POST http://localhost:18080/api/lottery/draw \
  -H "Content-Type: application/json" \
  -d '{
    "activityId": 1,
    "userOpenid": "test_user_001",
    "userNickname": "测试用户",
    "userAvatar": "",
    "tableId": null
  }'
```

### 3. 前端测试

启动前端项目，进入抽奖页面，点击抽奖按钮，检查是否能正常抽奖。

## 注意事项

1. 确保前端发送的JSON字段名与DTO中的属性名完全一致
2. 必填参数（activityId、userOpenid）如果为空会返回验证错误
3. 可选参数（userNickname、userAvatar、tableId）可以为null或空字符串

## 相关文件

- `DrawLotteryRequest.java` - 新增的DTO类
- `ApiLotteryController.java` - 修改的API控制器
- `LotteryRecordController.java` - 修改的系统管理控制器
