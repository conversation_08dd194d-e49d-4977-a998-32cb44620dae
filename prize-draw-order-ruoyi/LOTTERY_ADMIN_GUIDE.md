# 抽奖活动管理后台功能使用指南

## 功能概述

本次更新为管理后台增加了完整的抽奖活动管理功能，包括：

1. **抽奖活动管理** - 创建、编辑、删除抽奖活动
2. **抽奖记录管理** - 查看、管理用户抽奖记录
3. **统计功能** - 抽奖数据统计和分析

## 安装步骤

### 1. 数据库配置

执行以下SQL文件来添加菜单权限：

```bash
mysql -u root -p your_database < sql/lottery_menu.sql
```

### 2. 前端文件

已创建的前端文件：
- `ruoyi-ui/src/api/lottery/activity.js` - 抽奖活动API接口
- `ruoyi-ui/src/api/lottery/record.js` - 抽奖记录API接口
- `ruoyi-ui/src/views/lottery/activity/index.vue` - 抽奖活动列表页面
- `ruoyi-ui/src/views/lottery/activity/form.vue` - 抽奖活动表单页面
- `ruoyi-ui/src/views/lottery/record/index.vue` - 抽奖记录管理页面

### 3. 路由配置

已在 `ruoyi-ui/src/router/index.js` 中添加了相关路由配置。

## 功能使用说明

### 抽奖活动管理

#### 创建抽奖活动

1. 登录管理后台
2. 进入 "抽奖管理" -> "抽奖活动"
3. 点击 "新增" 按钮
4. 填写活动信息：
   - **基本信息**：活动名称、描述、开始/结束时间、抽奖次数限制
   - **奖品配置**：设置奖品名称、类型、价值、中奖概率、库存等
5. 确保所有奖品的中奖概率总和为100%
6. 点击 "保存" 完成创建

#### 管理抽奖活动

- **查看活动列表**：显示所有活动及其状态、统计信息
- **编辑活动**：修改活动信息和奖品配置
- **启用/停用**：控制活动状态
- **查看记录**：跳转到该活动的抽奖记录
- **删除活动**：删除不需要的活动
- **导出数据**：导出活动数据到Excel

### 抽奖记录管理

#### 查看抽奖记录

1. 进入 "抽奖管理" -> "抽奖记录"
2. 可以看到：
   - **统计卡片**：总抽奖次数、中奖次数、中奖率、未领取奖品数
   - **记录列表**：详细的抽奖记录信息

#### 管理中奖记录

- **筛选记录**：按活动、用户、中奖状态、领取状态筛选
- **标记已领取**：为中奖用户标记奖品已领取
- **删除记录**：删除无效记录
- **导出记录**：导出记录到Excel

## 权限说明

系统包含以下权限：

### 抽奖活动权限
- `lottery:activity:list` - 查看活动列表
- `lottery:activity:query` - 查询活动详情
- `lottery:activity:add` - 新增活动
- `lottery:activity:edit` - 编辑活动
- `lottery:activity:remove` - 删除活动
- `lottery:activity:export` - 导出活动数据

### 抽奖记录权限
- `lottery:record:list` - 查看记录列表
- `lottery:record:query` - 查询记录详情
- `lottery:record:edit` - 编辑记录（主要用于标记领取状态）
- `lottery:record:remove` - 删除记录
- `lottery:record:export` - 导出记录数据

## 注意事项

1. **奖品概率配置**：所有奖品的中奖概率总和必须等于100%
2. **活动时间**：结束时间必须大于开始时间
3. **商家权限**：系统支持商家数据隔离，每个商家只能管理自己的活动
4. **数据备份**：建议定期备份抽奖相关数据

## 待实现功能

以下功能在当前版本中暂未实现，可在后续版本中添加：

1. **活动复制功能** - 快速复制现有活动创建新活动
2. **批量领取标记** - 批量标记多个中奖记录为已领取
3. **更多统计图表** - 添加图表展示抽奖数据趋势
4. **活动模板** - 预设常用的活动模板

## 技术说明

- **前端框架**：Vue 2 + Element UI
- **后端框架**：Spring Boot + MyBatis
- **权限控制**：基于若依框架的RBAC权限系统
- **数据隔离**：支持商家级别的数据隔离

## 故障排除

### 常见问题

1. **菜单不显示**：检查是否执行了菜单SQL，用户是否有相应权限
2. **API调用失败**：检查后端服务是否正常启动，权限是否正确配置
3. **数据不显示**：检查商家数据权限配置是否正确

### 日志查看

可以通过以下方式查看相关日志：
- 后端日志：`logs/sys-info.log`
- 前端控制台：浏览器开发者工具Console面板

## 联系支持

如有问题，请联系技术支持团队。
