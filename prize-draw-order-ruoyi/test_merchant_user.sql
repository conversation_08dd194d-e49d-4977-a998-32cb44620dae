-- 测试商家用户关联功能的SQL脚本

-- 1. 查看当前sys_user表结构，确认merchant_id字段是否存在
DESCRIBE sys_user;

-- 2. 查看现有的商家数据
SELECT merchant_id, merchant_name, merchant_code, status FROM merchant WHERE del_flag = '0';

-- 3. 查看现有的用户数据，特别是merchant_id字段
SELECT user_id, user_name, nick_name, merchant_id, status, remark FROM sys_user WHERE del_flag = '0';

-- 4. 创建一个测试商家（如果不存在）
INSERT INTO merchant (merchant_name, merchant_code, contact_person, contact_phone, address, status, del_flag, create_by, create_time, remark)
VALUES ('测试商家001', 'TEST001', '张三', '13800138001', '测试地址001', '0', '0', 'admin', NOW(), '用于测试商家用户关联功能');

-- 5. 获取刚创建的商家ID
SET @merchant_id = LAST_INSERT_ID();

-- 6. 创建一个关联该商家的用户
INSERT INTO sys_user (user_name, nick_name, email, phonenumber, sex, avatar, password, status, del_flag, merchant_id, create_by, create_time, remark)
VALUES ('test_merchant_001', '测试商家用户001', '<EMAIL>', '13800138001', '0', '', '$2a$10$7JB720yubVSOfvVMe6/YqOPKYvuFI5wHrvi0QIDQ/.p.uROlFfmHa', '0', '0', @merchant_id, 'admin', NOW(), '测试商家用户');

-- 7. 为商家用户分配商家角色（假设角色ID为100）
INSERT INTO sys_user_role (user_id, role_id) VALUES (LAST_INSERT_ID(), 100);

-- 8. 验证数据是否正确插入
SELECT 
    u.user_id,
    u.user_name,
    u.nick_name,
    u.merchant_id,
    m.merchant_name,
    m.merchant_code,
    u.status as user_status,
    m.status as merchant_status
FROM sys_user u
LEFT JOIN merchant m ON u.merchant_id = m.merchant_id
WHERE u.user_name = 'test_merchant_001';

-- 9. 查看用户角色关联
SELECT 
    u.user_name,
    r.role_name,
    r.role_key
FROM sys_user u
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.user_name = 'test_merchant_001';

-- 10. 测试数据权限过滤（模拟查询商家桌台）
-- 假设当前登录用户是test_merchant_001，其merchant_id应该自动过滤数据
SELECT 
    t.table_id,
    t.table_number,
    t.table_name,
    t.merchant_id,
    m.merchant_name
FROM merchant_table t
LEFT JOIN merchant m ON t.merchant_id = m.merchant_id
WHERE t.merchant_id = @merchant_id;
