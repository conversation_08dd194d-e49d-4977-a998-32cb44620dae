package com.ruoyi.web.controller.api;

import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.service.IMerchantService;
import com.ruoyi.system.service.IMerchantConfigService;

/**
 * 商家API接口 - 小程序端调用
 *
 * <AUTHOR>
 */
@Anonymous
@RestController
@RequestMapping("/api/merchant")
public class ApiMerchantController extends BaseController
{
    @Autowired
    private IMerchantService merchantService;

    @Autowired
    private IMerchantConfigService merchantConfigService;

    /**
     * 根据商家编码获取商家信息
     */
    @GetMapping("/info/{merchantCode}")
    public AjaxResult getMerchantInfo(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        return success(merchant);
    }

    /**
     * 根据商家编码获取商家配置
     */
    @GetMapping("/config/{merchantCode}")
    public AjaxResult getMerchantConfig(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        Map<String, String> configMap = merchantConfigService.selectMerchantConfigMapByMerchantId(merchant.getMerchantId());
        return success(configMap);
    }

    /**
     * 根据商家编码和配置键获取配置值
     */
    @GetMapping("/config/{merchantCode}/{configKey}")
    public AjaxResult getMerchantConfigValue(@PathVariable String merchantCode, @PathVariable String configKey)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        String configValue = merchantConfigService.selectMerchantConfigValue(merchant.getMerchantId(), configKey);
        return success(configValue);
    }

    /**
     * 检查商家状态
     */
    @GetMapping("/check/{merchantCode}")
    public AjaxResult checkMerchantStatus(@PathVariable String merchantCode)
    {
        boolean isValid = merchantService.isMerchantValid(merchantCode);
        if (isValid) {
            return success("商家状态正常");
        } else {
            return error("商家已过期或状态异常，请联系管理员");
        }
    }
}
