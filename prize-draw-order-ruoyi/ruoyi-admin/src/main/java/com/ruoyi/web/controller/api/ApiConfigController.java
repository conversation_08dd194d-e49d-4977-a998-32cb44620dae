package com.ruoyi.web.controller.api;

import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.service.IMerchantService;
import com.ruoyi.system.service.IMerchantConfigService;

/**
 * 配置API接口 - 小程序端调用
 *
 * <AUTHOR>
 */
@Anonymous
@RestController
@RequestMapping("/api/config")
public class ApiConfigController extends BaseController
{
    @Autowired
    private IMerchantService merchantService;

    @Autowired
    private IMerchantConfigService merchantConfigService;

    /**
     * 根据商家编码获取所有配置
     */
    @GetMapping("/{merchantCode}")
    public AjaxResult getAllConfig(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        Map<String, String> configMap = merchantConfigService.selectMerchantConfigMapByMerchantId(merchant.getMerchantId());
        return success(configMap);
    }

    /**
     * 根据商家编码获取领取说明
     */
    @GetMapping("/claim-instruction/{merchantCode}")
    public AjaxResult getClaimInstruction(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        String claimInstruction = merchantConfigService.selectMerchantConfigValue(
            merchant.getMerchantId(), "claim_instruction");
        
        if (claimInstruction == null)
        {
            claimInstruction = "请到前台出示此页面领取奖品";
        }
        
        return success(claimInstruction);
    }

    /**
     * 根据商家编码获取扫码页面背景图片
     */
    @GetMapping("/scan-bg/{merchantCode}")
    public AjaxResult getScanPageBg(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        String scanPageBg = merchantConfigService.selectMerchantConfigValue(
            merchant.getMerchantId(), "scan_page_bg");
        
        if (scanPageBg == null)
        {
            scanPageBg = "/static/images/default_bg.jpg";
        }
        
        return success(scanPageBg);
    }

    /**
     * 根据商家编码获取抽奖页面背景图片
     */
    @GetMapping("/lottery-bg/{merchantCode}")
    public AjaxResult getLotteryBg(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        String lotteryBg = merchantConfigService.selectMerchantConfigValue(
            merchant.getMerchantId(), "lottery_bg");
        
        if (lotteryBg == null)
        {
            lotteryBg = "/static/images/lottery_bg.jpg";
        }
        
        return success(lotteryBg);
    }

    /**
     * 根据商家编码和配置键获取配置值
     */
    @GetMapping("/{merchantCode}/{configKey}")
    public AjaxResult getConfigValue(@PathVariable String merchantCode, @PathVariable String configKey)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        String configValue = merchantConfigService.selectMerchantConfigValue(merchant.getMerchantId(), configKey);
        return success(configValue);
    }
}
