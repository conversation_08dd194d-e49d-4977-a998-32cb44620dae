package com.ruoyi.web.controller.api;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.domain.MerchantTable;
import com.ruoyi.system.service.IMerchantService;
import com.ruoyi.system.service.IMerchantTableService;

/**
 * 桌台API接口 - 小程序端调用
 *
 * <AUTHOR>
 */
@Anonymous
@RestController
@RequestMapping("/api/table")
public class ApiTableController extends BaseController
{
    @Autowired
    private IMerchantService merchantService;

    @Autowired
    private IMerchantTableService merchantTableService;

    /**
     * 根据商家编码和桌台号获取桌台信息
     */
    @GetMapping("/{merchantCode}/{tableNumber}")
    public AjaxResult getTableInfo(@PathVariable String merchantCode, @PathVariable String tableNumber)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        MerchantTable table = merchantTableService.selectMerchantTableByMerchantIdAndNumber(
            merchant.getMerchantId(), tableNumber);
        if (table == null)
        {
            return error("桌台不存在");
        }
        
        if (!"0".equals(table.getStatus()))
        {
            return error("桌台已停用");
        }
        
        return success(table);
    }

    /**
     * 根据商家编码获取所有桌台列表
     */
    @GetMapping("/list/{merchantCode}")
    public AjaxResult getTableList(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        List<MerchantTable> tables = merchantTableService.selectMerchantTablesByMerchantId(merchant.getMerchantId());
        
        // 只返回状态正常的桌台
        tables.removeIf(table -> !"0".equals(table.getStatus()));
        
        return success(tables);
    }

    /**
     * 根据桌台ID获取桌台信息
     */
    @GetMapping("/info/{tableId}")
    public AjaxResult getTableById(@PathVariable Long tableId)
    {
        MerchantTable table = merchantTableService.selectMerchantTableById(tableId);
        if (table == null)
        {
            return error("桌台不存在");
        }
        
        if (!"0".equals(table.getStatus()))
        {
            return error("桌台已停用");
        }
        
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(table.getMerchantId().toString()))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        return success(table);
    }
}
