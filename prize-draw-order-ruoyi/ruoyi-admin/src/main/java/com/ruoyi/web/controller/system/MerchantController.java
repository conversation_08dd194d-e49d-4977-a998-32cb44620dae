package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.MerchantDataScope;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.service.IMerchantService;

/**
 * 商家管理 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/merchant")
public class MerchantController extends BaseController
{
    @Autowired
    private IMerchantService merchantService;

    /**
     * 获取商家列表
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:query')")
    @MerchantDataScope(merchantAlias = "")
    @GetMapping("/list")
    public TableDataInfo list(Merchant merchant)
    {
        startPage();
        List<Merchant> list = merchantService.selectMerchantList(merchant);
        return getDataTable(list);
    }

    /**
     * 导出商家列表
     */
    @Log(title = "商家管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('merchant:info:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, Merchant merchant)
    {
        List<Merchant> list = merchantService.selectMerchantList(merchant);
        ExcelUtil<Merchant> util = new ExcelUtil<Merchant>(Merchant.class);
        util.exportExcel(response, list, "商家数据");
    }

    /**
     * 根据商家编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:query')")
    @GetMapping(value = "/{merchantId}")
    public AjaxResult getInfo(@PathVariable Long merchantId)
    {
        return success(merchantService.selectMerchantById(merchantId));
    }

    /**
     * 根据商家编码查询商家信息
     */
    @GetMapping(value = "/code/{merchantCode}")
    public AjaxResult getByCode(@PathVariable String merchantCode)
    {
        return success(merchantService.selectMerchantByCode(merchantCode));
    }

    /**
     * 新增商家
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:add')")
    @Log(title = "商家管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody Merchant merchant)
    {
        if (!merchantService.checkMerchantCodeUnique(merchant))
        {
            return error("新增商家'" + merchant.getMerchantName() + "'失败，商家编码已存在");
        }
        merchant.setCreateBy(getUsername());
        return toAjax(merchantService.insertMerchant(merchant));
    }

    /**
     * 新增商家并创建用户账号
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:add')")
    @Log(title = "商家管理", businessType = BusinessType.INSERT)
    @PostMapping("/withUser")
    public AjaxResult addWithUser(@Validated @RequestBody Merchant merchant)
    {
        if (!merchantService.checkMerchantCodeUnique(merchant))
        {
            return error("新增商家'" + merchant.getMerchantName() + "'失败，商家编码已存在");
        }
        merchant.setCreateBy(getUsername());
        int result = merchantService.insertMerchantWithUser(merchant, true);
        if (result > 0)
        {
            return success("新增商家成功，已创建用户账号：" + merchant.getMerchantCode() + "，默认密码：123456");
        }
        else
        {
            return error("新增商家失败");
        }
    }

    /**
     * 修改商家
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:edit')")
    @Log(title = "商家管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Merchant merchant)
    {
        if (!merchantService.checkMerchantCodeUnique(merchant))
        {
            return error("修改商家'" + merchant.getMerchantCode() + "'失败，商家编码已存在");
        }
        merchant.setUpdateBy(getUsername());
        return toAjax(merchantService.updateMerchantWithUser(merchant));
    }

    /**
     * 删除商家
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:remove')")
    @Log(title = "商家管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{merchantIds}")
    public AjaxResult remove(@PathVariable Long[] merchantIds)
    {
        return toAjax(merchantService.deleteMerchantByIds(merchantIds));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:edit')")
    @Log(title = "商家管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody Merchant merchant)
    {
        merchant.setUpdateBy(getUsername());
        return toAjax(merchantService.updateMerchant(merchant));
    }

    /**
     * 校验商家编码
     */
    @PostMapping("/checkMerchantCodeUnique")
    public boolean checkMerchantCodeUnique(@RequestBody Merchant merchant)
    {
        return merchantService.checkMerchantCodeUnique(merchant);
    }

    /**
     * 查询即将到期的商家
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:query')")
    @GetMapping("/expiring/{days}")
    public AjaxResult getExpiringSoon(@PathVariable int days)
    {
        List<Merchant> list = merchantService.selectExpiringSoonMerchants(days);
        return success(list);
    }

    /**
     * 查询已过期的商家
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:query')")
    @GetMapping("/expired")
    public AjaxResult getExpired()
    {
        List<Merchant> list = merchantService.selectExpiredMerchants();
        return success(list);
    }

    /**
     * 更新过期商家状态
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:edit')")
    @Log(title = "商家管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateExpiredStatus")
    public AjaxResult updateExpiredStatus()
    {
        int count = merchantService.updateExpiredMerchantsStatus();
        return success("已更新 " + count + " 个过期商家的状态");
    }

    /**
     * 检查商家是否有效
     */
    @GetMapping("/valid/{merchantCode}")
    public AjaxResult checkValid(@PathVariable String merchantCode)
    {
        boolean isValid = merchantService.isMerchantValid(merchantCode);
        if (isValid) {
            return success("商家状态正常");
        } else {
            return error("商家已过期或状态异常，请联系管理员");
        }
    }

    /**
     * 为商家创建用户账号
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:add')")
    @Log(title = "商家管理", businessType = BusinessType.INSERT)
    @PostMapping("/createUser/{merchantId}")
    public AjaxResult createUser(@PathVariable Long merchantId)
    {
        Merchant merchant = merchantService.selectMerchantById(merchantId);
        if (merchant == null)
        {
            return error("商家不存在");
        }

        // 检查是否已存在用户
        SysUser existUser = merchantService.selectUserByMerchantId(merchantId);
        if (existUser != null)
        {
            return error("该商家已存在用户账号：" + existUser.getUserName());
        }

        Long userId = merchantService.createMerchantUser(merchant);
        if (userId != null)
        {
            return success("创建用户账号成功，用户名：" + merchant.getMerchantCode() + "，默认密码：123456");
        }
        else
        {
            return error("创建用户账号失败");
        }
    }

    /**
     * 查询商家关联的用户信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:query')")
    @GetMapping("/user/{merchantId}")
    public AjaxResult getMerchantUser(@PathVariable Long merchantId)
    {
        SysUser user = merchantService.selectUserByMerchantId(merchantId);
        return success(user);
    }
}
