# 抽奖功能测试指南

## 功能概述

本次修改实现了以下核心功能：
1. **同一微信号只能抽奖一次**：每个用户在同一活动中只能抽奖一次
2. **第二次进入显示结果**：用户再次进入时直接显示已抽取的奖品

## 测试环境准备

### 1. 数据库准备
```sql
-- 1. 执行数据库约束更新（可选，用于防止并发重复抽奖）
source sql/update_lottery_constraints.sql;

-- 2. 确保有测试数据
-- 插入测试商家
INSERT INTO merchant (merchant_code, merchant_name, contact_person, contact_phone, status, create_time) 
VALUES ('TEST001', '测试商家', '测试联系人', '13800138000', '0', NOW());

-- 插入测试活动
INSERT INTO lottery_activity (merchant_id, activity_name, activity_desc, start_time, end_time, prize_config, status, create_time)
VALUES (1, '测试抽奖活动', '这是一个测试活动', '2025-01-01 00:00:00', '2025-12-31 23:59:59', 
'[{"name":"一等奖","type":"prize","value":"100元","probability":10},{"name":"二等奖","type":"prize","value":"50元","probability":20},{"name":"谢谢参与","type":"none","value":"","probability":70}]', 
'0', NOW());
```

### 2. 应用启动
```bash
# 启动后端服务
cd prize-draw-order-ruoyi
mvn spring-boot:run

# 或者使用IDE启动 RuoYiApplication.main()
```

## 测试步骤

### 测试场景1：新用户首次抽奖

#### 步骤1：检查用户状态
```bash
# API测试
curl -X GET "http://localhost:8080/api/lottery/status/1/test_user_001"

# 预期响应
{
  "code": 200,
  "data": {
    "hasDrawn": false,
    "canDraw": true,
    "remainingDraws": 1
  }
}
```

#### 步骤2：执行抽奖
```bash
# API测试
curl -X POST "http://localhost:8080/api/lottery/draw" \
  -d "activityId=1&userOpenid=test_user_001&userNickname=测试用户"

# 预期响应
{
  "code": 200,
  "msg": "抽奖成功",
  "data": {
    "recordId": 1001,
    "activityId": 1,
    "userOpenid": "test_user_001",
    "prizeName": "一等奖",
    "isWinner": "1",
    "drawTime": "2025-01-31 10:30:00"
  }
}
```

#### 步骤3：验证状态更新
```bash
# 再次检查用户状态
curl -X GET "http://localhost:8080/api/lottery/status/1/test_user_001"

# 预期响应
{
  "code": 200,
  "data": {
    "hasDrawn": true,
    "canDraw": false,
    "remainingDraws": 0,
    "lotteryRecord": {
      "recordId": 1001,
      "prizeName": "一等奖",
      "isWinner": "1",
      "drawTime": "2025-01-31 10:30:00",
      "claimStatus": "0"
    }
  }
}
```

### 测试场景2：已抽奖用户尝试重复抽奖

#### 步骤1：尝试再次抽奖
```bash
# API测试
curl -X POST "http://localhost:8080/api/lottery/draw" \
  -d "activityId=1&userOpenid=test_user_001&userNickname=测试用户"

# 预期响应
{
  "code": 500,
  "msg": "您已经参与过此活动的抽奖或活动已结束"
}
```

### 测试场景3：小程序端测试

#### 步骤1：首次进入
1. 打开小程序抽奖页面
2. 观察页面显示抽奖转盘
3. 点击"开始抽奖"按钮
4. 观察抽奖动画和结果显示

#### 步骤2：再次进入
1. 刷新页面或重新进入
2. 观察页面直接显示抽奖结果
3. 确认没有抽奖转盘
4. 确认显示正确的奖品信息

## 验证要点

### 1. 后端验证
- [ ] `canUserParticipate` 方法正确判断用户是否可以抽奖
- [ ] `getUserRemainingDraws` 方法返回正确的剩余次数（0或1）
- [ ] 新增的 `/api/lottery/status` 接口返回正确的用户状态
- [ ] 重复抽奖请求被正确拒绝

### 2. 前端验证
- [ ] 页面根据用户状态正确渲染（转盘 vs 结果）
- [ ] 已抽奖用户看到正确的奖品信息
- [ ] 未抽奖用户可以正常抽奖
- [ ] 抽奖成功后页面状态正确更新

### 3. 数据库验证
```sql
-- 检查抽奖记录
SELECT * FROM lottery_record WHERE user_openid = 'test_user_001';

-- 验证同一用户在同一活动中只有一条记录
SELECT activity_id, user_openid, COUNT(*) as count 
FROM lottery_record 
GROUP BY activity_id, user_openid 
HAVING COUNT(*) > 1;
```

### 4. 边界情况验证
- [ ] 活动不存在时的错误处理
- [ ] 用户OpenID为空时的错误处理
- [ ] 网络异常时的错误处理
- [ ] 并发抽奖时的数据一致性

## 性能测试

### 1. 并发测试
```bash
# 使用ab工具测试并发抽奖
ab -n 100 -c 10 -p post_data.txt -T 'application/x-www-form-urlencoded' \
  http://localhost:8080/api/lottery/draw

# post_data.txt 内容：
# activityId=1&userOpenid=test_user_concurrent&userNickname=并发测试用户
```

### 2. 压力测试
```bash
# 测试大量用户同时访问状态接口
ab -n 1000 -c 50 http://localhost:8080/api/lottery/status/1/test_user_001
```

## 常见问题排查

### 1. 抽奖失败
- 检查活动是否存在且有效
- 检查用户是否已经抽过奖
- 检查数据库连接是否正常

### 2. 页面显示异常
- 检查API接口是否正常返回
- 检查前端状态变量是否正确设置
- 检查网络请求是否成功

### 3. 重复抽奖问题
- 检查数据库唯一约束是否生效
- 检查后端逻辑是否正确
- 检查前端防重复点击是否生效

## 测试完成标准

✅ 所有API接口测试通过
✅ 前端页面功能正常
✅ 数据库数据一致性验证通过
✅ 边界情况处理正确
✅ 性能测试满足要求
✅ 用户体验良好

## 部署注意事项

1. **数据库更新**：确保执行了约束更新脚本
2. **配置检查**：确认应用配置正确
3. **缓存清理**：清理相关缓存确保新逻辑生效
4. **监控设置**：设置相关监控确保系统稳定运行

## 回滚方案

如果发现问题需要回滚：

1. **代码回滚**：恢复到修改前的版本
2. **数据库回滚**：删除添加的唯一约束
3. **缓存清理**：清理相关缓存
4. **功能验证**：确认回滚后功能正常
