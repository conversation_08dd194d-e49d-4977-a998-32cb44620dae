# 抽奖功能测试文档

## 功能需求验证

### 1. 修改的功能点

#### 1.1 后端修改
- **LotteryActivityServiceImpl.canUserParticipate()**: 修改为检查用户是否已经抽过奖，而不是按天限制
- **LotteryActivityServiceImpl.getUserRemainingDraws()**: 修改为返回0或1（已抽过或未抽过）
- **ApiLotteryController.getUserLotteryStatus()**: 新增API接口，返回用户抽奖状态

#### 1.2 前端修改
- **api.js**: 新增getUserLotteryStatus API调用
- **lottery.vue**: 
  - 新增hasDrawn和lotteryResult状态变量
  - 新增checkUserLotteryStatus方法
  - 修改页面渲染逻辑，已抽奖用户显示结果，未抽奖用户显示转盘
  - 修改startLottery方法，防止已抽奖用户重复抽奖

### 2. 测试场景

#### 2.1 场景1：新用户首次进入
**预期行为**:
1. 用户进入抽奖页面
2. 系统调用getUserLotteryStatus API
3. 返回hasDrawn=false, canDraw=true, remainingDraws=1
4. 页面显示抽奖转盘
5. 用户可以点击开始抽奖

#### 2.2 场景2：用户抽奖成功后
**预期行为**:
1. 用户点击抽奖
2. 系统执行抽奖逻辑
3. 抽奖成功后更新状态：hasDrawn=true, lotteryResult=抽奖结果
4. 页面显示抽奖结果，隐藏转盘

#### 2.3 场景3：已抽奖用户再次进入
**预期行为**:
1. 用户进入抽奖页面
2. 系统调用getUserLotteryStatus API
3. 返回hasDrawn=true, canDraw=false, remainingDraws=0, lotteryRecord=历史记录
4. 页面直接显示抽奖结果，不显示转盘
5. 用户无法再次抽奖

#### 2.4 场景4：已抽奖用户尝试重复抽奖
**预期行为**:
1. 如果用户通过某种方式触发抽奖按钮
2. 系统检查hasDrawn状态
3. 显示"您已经抽过奖了"提示
4. 不执行抽奖逻辑

### 3. API接口测试

#### 3.1 新增接口：GET /api/lottery/status/{activityId}/{userOpenid}

**测试用例1：未抽奖用户**
```
请求：GET /api/lottery/status/1/test_user_001
预期响应：
{
  "code": 200,
  "data": {
    "hasDrawn": false,
    "canDraw": true,
    "remainingDraws": 1
  }
}
```

**测试用例2：已抽奖用户**
```
请求：GET /api/lottery/status/1/test_user_002
预期响应：
{
  "code": 200,
  "data": {
    "hasDrawn": true,
    "canDraw": false,
    "remainingDraws": 0,
    "lotteryRecord": {
      "recordId": 123,
      "prizeName": "一等奖",
      "isWinner": "1",
      "drawTime": "2025-01-31 10:30:00",
      "claimStatus": "0"
    }
  }
}
```

### 4. 数据库验证

#### 4.1 验证抽奖限制逻辑
```sql
-- 检查用户在指定活动中的抽奖次数
SELECT COUNT(*) FROM lottery_record 
WHERE activity_id = 1 AND user_openid = 'test_user_001';

-- 如果返回0，用户可以抽奖
-- 如果返回>0，用户不能再抽奖
```

#### 4.2 验证抽奖记录
```sql
-- 查看用户的抽奖记录
SELECT * FROM lottery_record 
WHERE activity_id = 1 AND user_openid = 'test_user_001'
ORDER BY draw_time DESC;
```

### 5. 前端页面验证

#### 5.1 页面状态检查
- 检查hasDrawn变量是否正确设置
- 检查lotteryResult是否包含正确的抽奖记录
- 检查页面是否根据状态正确渲染（转盘 vs 结果展示）

#### 5.2 用户交互验证
- 未抽奖用户：可以看到转盘，可以点击抽奖
- 已抽奖用户：看到结果展示，无法再次抽奖

### 6. 边界情况测试

#### 6.1 活动不存在
- 请求不存在的活动ID
- 预期返回错误信息

#### 6.2 用户OpenID为空
- 传入空的用户OpenID
- 预期返回错误信息

#### 6.3 网络异常
- 模拟网络请求失败
- 检查前端错误处理是否正确

### 7. 性能测试

#### 7.1 并发抽奖测试
- 同一用户同时发起多个抽奖请求
- 验证是否只有一个请求成功，其他请求被正确拒绝

#### 7.2 大量用户测试
- 模拟大量用户同时访问抽奖页面
- 验证系统性能和稳定性

## 测试结论

通过以上测试场景的验证，可以确保：
1. 同一微信号只能在同一活动中抽奖一次
2. 第二次进入时直接显示已抽取的奖品
3. 系统具有良好的用户体验和错误处理机制
4. 数据一致性和安全性得到保障
