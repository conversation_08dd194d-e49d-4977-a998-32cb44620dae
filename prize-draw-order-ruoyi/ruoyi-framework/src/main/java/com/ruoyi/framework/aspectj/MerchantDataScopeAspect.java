package com.ruoyi.framework.aspectj;

import java.util.ArrayList;
import java.util.List;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import com.ruoyi.common.annotation.MerchantDataScope;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;

/**
 * 商家数据权限过滤处理
 * 
 * <AUTHOR>
 */
@Aspect
@Component
public class MerchantDataScopeAspect
{
    /**
     * 商家数据权限过滤关键字
     */
    public static final String MERCHANT_DATA_SCOPE_FILTER = "merchantDataScopeFilter";

    @Before("@annotation(controllerMerchantDataScope)")
    public void doBefore(JoinPoint point, MerchantDataScope controllerMerchantDataScope) throws Throwable
    {
        clearDataScope(point);
        handleDataScope(point, controllerMerchantDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, MerchantDataScope controllerMerchantDataScope)
    {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNotNull(loginUser))
        {
            SysUser currentUser = loginUser.getUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser) && !currentUser.isAdmin())
            {
                String permission = StringUtils.defaultIfEmpty(controllerMerchantDataScope.permission(), 
                    MethodSignature.class.cast(joinPoint.getSignature()).getMethod().getName());
                dataScopeFilter(joinPoint, currentUser, controllerMerchantDataScope.merchantAlias(), permission);
            }
        }
    }

    /**
     * 数据范围过滤
     * 
     * @param joinPoint 切点
     * @param user 用户
     * @param merchantAlias 商家别名
     * @param permission 权限字符
     */
    public static void dataScopeFilter(JoinPoint joinPoint, SysUser user, String merchantAlias, String permission)
    {
        StringBuilder sqlString = new StringBuilder();
        List<String> conditions = new ArrayList<>();

        // 获取用户关联的商家ID
        Long merchantId = getMerchantIdByUser(user);
        
        if (merchantId != null)
        {
            if (StringUtils.isNotBlank(merchantAlias))
            {
                conditions.add(String.format("%s.merchant_id = %d", merchantAlias, merchantId));
            }
            else
            {
                conditions.add(String.format("merchant_id = %d", merchantId));
            }
        }

        if (!conditions.isEmpty())
        {
            sqlString.append(" AND (").append(String.join(" OR ", conditions)).append(")");
        }

        if (StringUtils.isNotBlank(sqlString.toString()))
        {
            Object params = joinPoint.getArgs()[0];
            if (StringUtils.isNotNull(params) && params instanceof BaseEntity)
            {
                BaseEntity baseEntity = (BaseEntity) params;
                baseEntity.getParams().put(MERCHANT_DATA_SCOPE_FILTER, sqlString.toString());
            }
        }
    }

    /**
     * 获取用户关联的商家ID
     *
     * @param user 用户信息
     * @return 商家ID
     */
    private static Long getMerchantIdByUser(SysUser user)
    {
        // 直接从用户表的merchant_id字段获取商家ID
        if (user.getMerchantId() != null)
        {
            return user.getMerchantId();
        }

        // 兼容旧的实现：从用户备注中获取商家ID
        if (StringUtils.isNotEmpty(user.getRemark()) && user.getRemark().startsWith("merchantId:"))
        {
            try
            {
                return Long.parseLong(user.getRemark().substring("merchantId:".length()));
            }
            catch (NumberFormatException e)
            {
                return null;
            }
        }

        return null;
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint)
    {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params) && params instanceof BaseEntity)
        {
            BaseEntity baseEntity = (BaseEntity) params;
            baseEntity.getParams().put(MERCHANT_DATA_SCOPE_FILTER, "");
        }
    }
}
