import request from "@/utils/request";

// 查询商家列表
export function listMerchant(query) {
  return request({
    url: "/system/merchant/list",
    method: "get",
    params: query,
  });
}

// 查询商家详细
export function getMerchant(merchantId) {
  return request({
    url: "/system/merchant/" + merchantId,
    method: "get",
  });
}

// 根据商家编码查询商家信息
export function getMerchantByCode(merchantCode) {
  return request({
    url: "/system/merchant/code/" + merchantCode,
    method: "get",
  });
}

// 新增商家
export function addMerchant(data) {
  return request({
    url: "/system/merchant",
    method: "post",
    data: data,
  });
}

// 新增商家并创建用户账号
export function addMerchantWithUser(data) {
  return request({
    url: "/system/merchant/withUser",
    method: "post",
    data: data,
  });
}

// 修改商家
export function updateMerchant(data) {
  return request({
    url: "/system/merchant",
    method: "put",
    data: data,
  });
}

// 删除商家
export function delMerchant(merchantId) {
  return request({
    url: "/system/merchant/" + merchantId,
    method: "delete",
  });
}

// 状态修改
export function changeStatus(merchantId, status) {
  const data = {
    merchantId,
    status,
  };
  return request({
    url: "/system/merchant/changeStatus",
    method: "put",
    data: data,
  });
}

// 校验商家编码
export function checkMerchantCodeUnique(merchant) {
  return request({
    url: "/system/merchant/checkMerchantCodeUnique",
    method: "post",
    data: merchant,
  });
}

// 查询即将到期的商家
export function getExpiringSoon(days) {
  return request({
    url: "/system/merchant/expiring/" + days,
    method: "get",
  });
}

// 查询已过期的商家
export function getExpiredMerchants() {
  return request({
    url: "/system/merchant/expired",
    method: "get",
  });
}

// 更新过期商家状态
export function updateExpiredStatus() {
  return request({
    url: "/system/merchant/updateExpiredStatus",
    method: "post",
  });
}

// 导出商家
export function exportMerchant(query) {
  return request({
    url: "/system/merchant/export",
    method: "post",
    params: query,
  });
}

// 检查商家是否有效
export function checkMerchantValid(merchantCode) {
  return request({
    url: "/system/merchant/valid/" + merchantCode,
    method: "get",
  });
}

// 为商家创建用户账号
export function createMerchantUser(merchantId) {
  return request({
    url: "/system/merchant/createUser/" + merchantId,
    method: "post",
  });
}

// 查询商家关联的用户信息
export function getMerchantUser(merchantId) {
  return request({
    url: "/system/merchant/user/" + merchantId,
    method: "get",
  });
}
