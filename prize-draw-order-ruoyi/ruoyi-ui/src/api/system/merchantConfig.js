import request from "@/utils/request";

// 查询配置列表
export function listMerchantConfig(query) {
  return request({
    url: "/merchant/config/list",
    method: "get",
    params: query,
  });
}

// 查询配置详细
export function getMerchantConfig(configId) {
  return request({
    url: "/merchant/config/" + configId,
    method: "get",
  });
}

// 根据商家ID查询配置列表
export function getMerchantConfigsByMerchantId(merchantId) {
  return request({
    url: "/merchant/config/merchant/" + merchantId,
    method: "get",
  });
}

// 根据商家ID查询配置Map
export function getMerchantConfigMapByMerchantId(merchantId) {
  return request({
    url: "/merchant/config/merchant/" + merchantId + "/map",
    method: "get",
  });
}

// 根据商家ID和配置键查询配置值
export function getMerchantConfigValue(merchantId, configKey) {
  return request({
    url: "/merchant/config/merchant/" + merchantId + "/key/" + configKey,
    method: "get",
  });
}

// 新增配置
export function addMerchantConfig(data) {
  return request({
    url: "/merchant/config",
    method: "post",
    data: data,
  });
}

// 修改配置
export function updateMerchantConfig(data) {
  return request({
    url: "/merchant/config",
    method: "put",
    data: data,
  });
}

// 删除配置
export function delMerchantConfig(configId) {
  return request({
    url: "/merchant/config/" + configId,
    method: "delete",
  });
}

// 批量保存或更新配置
export function batchSaveOrUpdateConfig(merchantId, configMap) {
  return request({
    url: "/merchant/config/batchSaveOrUpdate/" + merchantId,
    method: "post",
    data: configMap,
  });
}

// 校验配置键唯一性
export function checkConfigKeyUnique(merchantConfig) {
  return request({
    url: "/merchant/config/checkConfigKeyUnique",
    method: "post",
    data: merchantConfig,
  });
}
