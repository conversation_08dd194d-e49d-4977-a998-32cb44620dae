import request from "@/utils/request";

// 查询桌台列表
export function listMerchantTable(query) {
  return request({
    url: "/merchant/table/list",
    method: "get",
    params: query,
  });
}

// 查询桌台详细
export function getMerchantTable(tableId) {
  return request({
    url: "/merchant/table/" + tableId,
    method: "get",
  });
}

// 根据商家ID查询桌台列表
export function getMerchantTablesByMerchantId(merchantId) {
  return request({
    url: "/merchant/table/merchant/" + merchantId,
    method: "get",
  });
}

// 新增桌台
export function addMerchantTable(data) {
  return request({
    url: "/merchant/table",
    method: "post",
    data: data,
  });
}

// 修改桌台
export function updateMerchantTable(data) {
  return request({
    url: "/merchant/table",
    method: "put",
    data: data,
  });
}

// 删除桌台
export function delMerchantTable(tableId) {
  return request({
    url: "/merchant/table/" + tableId,
    method: "delete",
  });
}

// 状态修改
export function changeTableStatus(tableId, status) {
  const data = {
    tableId,
    status,
  };
  return request({
    url: "/merchant/table/changeStatus",
    method: "put",
    data: data,
  });
}

// 校验桌台号唯一性
export function checkTableNumberUnique(merchantTable) {
  return request({
    url: "/merchant/table/checkTableNumberUnique",
    method: "post",
    data: merchantTable,
  });
}

// 生成桌台二维码
export function generateTableQrCode(tableId) {
  return request({
    url: "/merchant/table/generateQrCode/" + tableId,
    method: "post",
  });
}

// 批量生成桌台二维码
export function batchGenerateQrCode(merchantId) {
  return request({
    url: "/system/merchantTable/batchGenerateQrCode/" + merchantId,
    method: "post",
  });
}
