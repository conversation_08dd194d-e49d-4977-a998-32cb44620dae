import request from "@/utils/request";

// 查询抽奖记录列表
export function listLotteryRecord(query) {
  return request({
    url: "/lottery/record/list",
    method: "get",
    params: query,
  });
}

// 查询抽奖记录详细
export function getLotteryRecord(recordId) {
  return request({
    url: "/lottery/record/" + recordId,
    method: "get",
  });
}

// 根据活动ID查询抽奖记录列表
export function getLotteryRecordsByActivityId(activityId) {
  return request({
    url: "/lottery/record/activity/" + activityId,
    method: "get",
  });
}

// 根据商家ID查询抽奖记录列表
export function getLotteryRecordsByMerchantId(merchantId) {
  return request({
    url: "/lottery/record/merchant/" + merchantId,
    method: "get",
  });
}

// 根据用户OpenID查询抽奖记录
export function getLotteryRecordsByUserOpenid(userOpenid) {
  return request({
    url: "/lottery/record/user/" + userOpenid,
    method: "get",
  });
}

// 查询中奖记录
export function getWinningRecords(query) {
  return request({
    url: "/lottery/record/winning",
    method: "get",
    params: query,
  });
}

// 查询未领取的中奖记录
export function getUnclaimedRecords(query) {
  return request({
    url: "/lottery/record/unclaimed",
    method: "get",
    params: query,
  });
}

// 修改抽奖记录
export function updateLotteryRecord(data) {
  return request({
    url: "/lottery/record",
    method: "put",
    data: data,
  });
}

// 删除抽奖记录
export function delLotteryRecord(recordId) {
  return request({
    url: "/lottery/record/" + recordId,
    method: "delete",
  });
}

// 批量删除抽奖记录
export function delLotteryRecords(recordIds) {
  return request({
    url: "/lottery/record/" + recordIds,
    method: "delete",
  });
}

// 导出抽奖记录
export function exportLotteryRecord(query) {
  return request({
    url: "/lottery/record/export",
    method: "post",
    params: query,
  });
}

// 标记奖品为已领取
export function claimPrize(recordId) {
  return request({
    url: "/lottery/record/claim/" + recordId,
    method: "post",
  });
}

// 批量标记奖品为已领取 - 暂时注释，后端接口待实现
// export function batchClaimPrize(recordIds) {
//   return request({
//     url: "/lottery/record/claim/batch",
//     method: "put",
//     data: recordIds,
//   });
// }

// 获取抽奖统计信息
export function getLotteryStatistics(query) {
  return request({
    url: "/lottery/record/statistics",
    method: "get",
    params: query,
  });
}

// 获取活动抽奖统计
export function getActivityLotteryStatistics(activityId) {
  return request({
    url: "/lottery/record/statistics/activity/" + activityId,
    method: "get",
  });
}

// 获取商家抽奖统计
export function getMerchantLotteryStatistics(merchantId) {
  return request({
    url: "/lottery/record/statistics/merchant/" + merchantId,
    method: "get",
  });
}

// 获取今日抽奖统计
export function getTodayLotteryStatistics(merchantId) {
  return request({
    url: "/lottery/record/statistics/today/" + merchantId,
    method: "get",
  });
}
