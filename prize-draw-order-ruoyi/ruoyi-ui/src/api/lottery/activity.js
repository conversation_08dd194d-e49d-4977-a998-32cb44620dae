import request from "@/utils/request";

// 查询抽奖活动列表
export function listLotteryActivity(query) {
  return request({
    url: "/lottery/activity/list",
    method: "get",
    params: query,
  });
}

// 查询抽奖活动详细
export function getLotteryActivity(activityId) {
  return request({
    url: "/lottery/activity/" + activityId,
    method: "get",
  });
}

// 根据商家ID查询抽奖活动列表
export function getLotteryActivitiesByMerchantId(merchantId) {
  return request({
    url: "/lottery/activity/merchant/" + merchantId,
    method: "get",
  });
}

// 查询有效的抽奖活动列表
export function getValidLotteryActivities(merchantId) {
  return request({
    url: "/lottery/activity/valid/" + merchantId,
    method: "get",
  });
}

// 获取当前有效的抽奖活动
export function getCurrentValidActivity(merchantId) {
  return request({
    url: "/lottery/activity/current/" + merchantId,
    method: "get",
  });
}

// 新增抽奖活动
export function addLotteryActivity(data) {
  return request({
    url: "/lottery/activity",
    method: "post",
    data: data,
  });
}

// 修改抽奖活动
export function updateLotteryActivity(data) {
  return request({
    url: "/lottery/activity",
    method: "put",
    data: data,
  });
}

// 删除抽奖活动
export function delLotteryActivity(activityId) {
  return request({
    url: "/lottery/activity/" + activityId,
    method: "delete",
  });
}

// 批量删除抽奖活动
export function delLotteryActivities(activityIds) {
  return request({
    url: "/lottery/activity/" + activityIds,
    method: "delete",
  });
}

// 导出抽奖活动
export function exportLotteryActivity(query) {
  return request({
    url: "/lottery/activity/export",
    method: "post",
    params: query,
  });
}

// 修改活动状态
export function changeActivityStatus(data) {
  return request({
    url: "/lottery/activity/changeStatus",
    method: "put",
    data: data,
  });
}

// 统计商家活动数量
export function countActivitiesByMerchantId(merchantId) {
  return request({
    url: "/lottery/activity/count/" + merchantId,
    method: "get",
  });
}

// 查询即将结束的活动
export function getEndingSoonActivities(hours) {
  return request({
    url: "/lottery/activity/ending/" + hours,
    method: "get",
  });
}

// 复制活动 - 暂时注释，后端接口待实现
// export function copyLotteryActivity(activityId) {
//   return request({
//     url: "/lottery/activity/copy/" + activityId,
//     method: "post",
//   });
// }

// 获取活动统计信息
export function getActivityStatistics(activityId) {
  return request({
    url: "/lottery/activity/statistics/" + activityId,
    method: "get",
  });
}
