<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九宫格抽奖预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .lottery-container {
            max-width: 400px;
            margin: 0 auto;
        }

        .merchant-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .merchant-name {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 5px;
        }

        .activity-name {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
        }

        .lottery-grid-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .grid-wrapper {
            width: 300px;
            height: 300px;
        }

        .lottery-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 4px;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 10px;
        }

        .grid-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }

        .grid-item.active {
            background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }

        .grid-item.active .prize-name {
            color: #fff;
            font-weight: bold;
        }

        .grid-item.center {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .grid-item.center:hover {
            transform: scale(1.02);
        }

        .grid-item.center:active {
            transform: scale(0.98);
        }

        .prize-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            padding: 5px;
        }

        .prize-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .prize-name {
            font-size: 12px;
            color: #333;
            line-height: 1.2;
        }

        .center-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #fff;
        }

        .center-text {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .remaining-text {
            font-size: 10px;
            opacity: 0.9;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-2px);
            }
        }

        .grid-item.active .prize-icon {
            animation: bounce 0.6s ease-in-out;
        }

        .prize-list {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .prize-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        }

        .prize-items {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .prize-list .prize-item {
            flex: 1;
            min-width: 100px;
            background: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
        }

        .prize-list .prize-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
        }

        .prize-probability {
            font-size: 12px;
            color: #666;
        }

        .controls {
            text-align: center;
            margin-top: 20px;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            margin: 0 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="lottery-container">
        <!-- 商家信息 -->
        <div class="merchant-header">
            <div class="merchant-name">测试商家</div>
            <div class="activity-name">九宫格抽奖活动</div>
        </div>

        <!-- 九宫格抽奖 -->
        <div class="lottery-grid-container">
            <div class="grid-wrapper">
                <div class="lottery-grid" id="lotteryGrid">
                    <!-- 九宫格项目将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 奖品列表 -->
        <div class="prize-list">
            <div class="prize-title">奖品设置</div>
            <div class="prize-items" id="prizeItems">
                <!-- 奖品列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls">
            <button class="btn" onclick="resetLottery()">重置</button>
            <button class="btn" onclick="startDemo()">开始演示</button>
        </div>
    </div>

    <script>
        // 奖品数据
        const prizeList = [
            { prizeName: '一等奖', probability: 5 },
            { prizeName: '二等奖', probability: 10 },
            { prizeName: '三等奖', probability: 15 },
            { prizeName: '四等奖', probability: 20 },
            { prizeName: '五等奖', probability: 25 },
            { prizeName: '六等奖', probability: 15 },
            { prizeName: '谢谢参与', probability: 10 }
        ];

        let gridItems = [];
        let currentIndex = -1;
        let isDrawing = false;
        let remainingDraws = 3;
        let animationTimer = null;

        // 初始化九宫格
        function initGrid() {
            const prizes = [...prizeList];
            while (prizes.length < 8) {
                prizes.push({ prizeName: '谢谢参与', probability: 0 });
            }
            if (prizes.length > 8) {
                prizes.splice(8);
            }
            gridItems = prizes;
            renderGrid();
            renderPrizeList();
        }

        // 渲染九宫格
        function renderGrid() {
            const grid = document.getElementById('lotteryGrid');
            grid.innerHTML = '';
            
            for (let i = 0; i < 9; i++) {
                const item = document.createElement('div');
                item.className = 'grid-item';
                
                if (i === 4) {
                    // 中心按钮
                    item.className += ' center';
                    item.innerHTML = `
                        <div class="center-button">
                            <div class="center-text">${isDrawing ? '抽奖中...' : '点击抽奖'}</div>
                            <div class="remaining-text">剩余${remainingDraws}次</div>
                        </div>
                    `;
                    item.onclick = startLottery;
                } else {
                    // 奖品项
                    const prizeIndex = i > 4 ? i - 1 : i;
                    const prize = gridItems[prizeIndex];
                    item.innerHTML = `
                        <div class="prize-item">
                            <div class="prize-icon">🎁</div>
                            <div class="prize-name">${prize.prizeName}</div>
                        </div>
                    `;
                }
                
                if (currentIndex !== -1) {
                    const activeIndex = currentIndex > 4 ? currentIndex + 1 : currentIndex;
                    if (i === activeIndex) {
                        item.className += ' active';
                    }
                }
                
                grid.appendChild(item);
            }
        }

        // 渲染奖品列表
        function renderPrizeList() {
            const container = document.getElementById('prizeItems');
            container.innerHTML = '';
            
            prizeList.forEach(prize => {
                const item = document.createElement('div');
                item.className = 'prize-item';
                item.innerHTML = `
                    <div class="prize-name">${prize.prizeName}</div>
                    <div class="prize-probability">中奖率：${prize.probability}%</div>
                `;
                container.appendChild(item);
            });
        }

        // 开始抽奖
        function startLottery() {
            if (isDrawing || remainingDraws <= 0) return;
            
            isDrawing = true;
            remainingDraws--;
            
            const targetIndex = Math.floor(Math.random() * 8);
            startGridAnimation(targetIndex, () => {
                showResult(gridItems[targetIndex]);
                isDrawing = false;
                renderGrid();
            });
        }

        // 九宫格动画
        function startGridAnimation(targetIndex, callback) {
            let animationCount = 0;
            let animationSpeed = 100;
            const totalRounds = 3;
            const totalSteps = totalRounds * 8 + targetIndex;
            
            const animate = () => {
                currentIndex = animationCount % 8;
                animationCount++;
                renderGrid();
                
                if (animationCount > totalSteps - 10) {
                    animationSpeed = 200;
                } else if (animationCount > totalSteps - 20) {
                    animationSpeed = 150;
                }
                
                if (animationCount >= totalSteps) {
                    currentIndex = targetIndex;
                    renderGrid();
                    clearTimeout(animationTimer);
                    setTimeout(callback, 500);
                } else {
                    animationTimer = setTimeout(animate, animationSpeed);
                }
            };
            
            animate();
        }

        // 显示结果
        function showResult(result) {
            const isWinner = result.prizeName !== '谢谢参与';
            const title = isWinner ? '恭喜中奖！' : '很遗憾';
            const content = isWinner ? `您获得了：${result.prizeName}` : '谢谢参与，再接再厉！';
            alert(`${title}\n${content}`);
        }

        // 重置抽奖
        function resetLottery() {
            currentIndex = -1;
            isDrawing = false;
            remainingDraws = 3;
            clearTimeout(animationTimer);
            renderGrid();
        }

        // 开始演示
        function startDemo() {
            if (!isDrawing && remainingDraws > 0) {
                startLottery();
            }
        }

        // 初始化页面
        initGrid();
    </script>
</body>
</html>
