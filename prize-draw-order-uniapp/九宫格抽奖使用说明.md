# 九宫格抽奖功能使用说明

## 功能概述

九宫格抽奖是一个基于3x3网格布局的抽奖系统，用户点击中心按钮开始抽奖，系统会以跑马灯的形式高亮显示各个奖品位置，最终停在中奖奖品上。

## 界面布局

```
┌─────┬─────┬─────┐
│ 奖品1│ 奖品2│ 奖品3│
├─────┼─────┼─────┤
│ 奖品8│抽奖按钮│ 奖品4│
├─────┼─────┼─────┤
│ 奖品7│ 奖品6│ 奖品5│
└─────┴─────┴─────┘
```

## 主要特性

### 1. 智能奖品配置
- 自动从后台获取奖品配置
- 支持最多8个奖品位置
- 奖品不足时自动用"谢谢参与"填充
- 奖品超过8个时自动截取前8个

### 2. 流畅动画效果
- 跑马灯式高亮动画
- 动画速度逐渐减缓
- 转3圈后停在目标位置
- 支持弹跳动画效果

### 3. 用户体验优化
- 中心按钮显示剩余次数
- 抽奖中状态提示
- 中奖结果弹窗展示
- 响应式设计适配各种屏幕

## 技术实现

### 数据结构
```javascript
// 九宫格数据
gridItems: [
  { prizeName: '一等奖', prizeType: 'physical', probability: 5 },
  { prizeName: '二等奖', prizeType: 'coupon', probability: 10 },
  // ... 最多8个奖品
]

// 动画状态
currentIndex: -1,        // 当前高亮位置
isDrawing: false,        // 是否正在抽奖
animationTimer: null,    // 动画定时器
targetIndex: -1          // 目标中奖位置
```

### 核心方法

#### 1. initGrid() - 初始化九宫格
- 处理奖品数据
- 确保有8个位置
- 按顺序排列奖品

#### 2. startGridAnimation() - 开始动画
- 设置动画参数
- 循环高亮显示
- 逐渐减速效果
- 停在目标位置

#### 3. startLottery() - 开始抽奖
- 验证抽奖条件
- 调用后台接口
- 启动动画效果
- 显示抽奖结果

## 样式设计

### CSS Grid布局
```scss
.lottery-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 8rpx;
}
```

### 动画效果
```scss
.grid-item.active {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  transform: scale(1.05);
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10rpx); }
  60% { transform: translateY(-5rpx); }
}
```

## 后台配置

### 奖品配置格式
```json
[
  {
    "prizeName": "一等奖",
    "prizeType": "physical",
    "prizeValue": "100元代金券",
    "probability": 5,
    "totalCount": 10,
    "dailyCount": 2,
    "prizeImage": ""
  }
]
```

### 配置说明
- `prizeName`: 奖品名称（必填）
- `prizeType`: 奖品类型（physical/coupon/points/thanks）
- `prizeValue`: 奖品价值描述
- `probability`: 中奖概率（百分比）
- `totalCount`: 总库存数量
- `dailyCount`: 每日限制数量
- `prizeImage`: 奖品图片URL

## 使用方法

### 1. 商家后台设置
1. 登录管理后台
2. 进入"抽奖管理" -> "抽奖活动"
3. 创建或编辑活动
4. 配置奖品信息（最多8个）
5. 设置中奖概率（总和100%）
6. 保存并启用活动

### 2. 用户抽奖流程
1. 扫描桌台二维码
2. 进入抽奖页面
3. 查看奖品设置
4. 点击中心"点击抽奖"按钮
5. 观看动画效果
6. 查看中奖结果
7. 领取奖品（如中奖）

## 注意事项

1. **奖品数量限制**：最多支持8个奖品位置
2. **概率配置**：所有奖品概率总和应为100%
3. **动画性能**：在低端设备上可能需要优化动画效果
4. **网络延迟**：抽奖接口调用可能有延迟，需要适当的loading状态
5. **用户体验**：确保动画时长适中，不要过长或过短

## 文件说明

- `pages/lottery/lottery.vue` - 主抽奖页面
- `pages/lottery/demo.vue` - 演示页面
- `lottery-preview.html` - 浏览器预览页面
- `utils/api.js` - API接口配置

## 预览方式

1. **浏览器预览**：直接打开 `lottery-preview.html`
2. **小程序预览**：运行项目后访问 `/pages/lottery/demo`
3. **实际测试**：配置后台数据后访问 `/pages/lottery/lottery`
