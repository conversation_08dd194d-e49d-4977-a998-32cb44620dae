<template>
  <view class="webview-container">
    <web-view :src="url" @message="handleMessage"></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      url: ''
    }
  },
  
  onLoad(options) {
    this.url = decodeURIComponent(options.url || '')
    
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '点餐'
    })
  },
  
  methods: {
    handleMessage(event) {
      console.log('WebView消息:', event.detail.data)
    }
  }
}
</script>

<style lang="scss" scoped>
.webview-container {
  height: 100vh;
}
</style>
