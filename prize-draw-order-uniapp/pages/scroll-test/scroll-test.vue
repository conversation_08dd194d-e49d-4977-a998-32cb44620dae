<template>
  <scroll-view class="scroll-container" scroll-y="true" enable-back-to-top="true">
    <view class="content">
      <view class="section">
        <text class="title">滚动测试页面</text>
        <text class="desc">这个页面用来测试滚动功能是否正常工作</text>
      </view>
      
      <view class="section" v-for="i in 20" :key="i">
        <view class="item">
          <text class="item-title">测试项目 {{ i }}</text>
          <text class="item-desc">这是第 {{ i }} 个测试项目，用来测试页面滚动功能。当内容超出屏幕高度时，页面应该能够正常滚动。</text>
        </view>
      </view>
      
      <view class="section">
        <text class="footer">页面底部 - 如果能看到这里说明滚动功能正常</text>
      </view>
    </view>
  </scroll-view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  }
}
</script>

<style lang="scss" scoped>
.scroll-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.content {
  padding: 40rpx 30rpx;
  min-height: 100vh;
}

.section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  text-align: center;
  margin-bottom: 20rpx;
}

.desc {
  font-size: 28rpx;
  color: #666;
  display: block;
  text-align: center;
}

.item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
}

.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.item-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
  line-height: 1.5;
}

.footer {
  font-size: 32rpx;
  color: #333;
  display: block;
  text-align: center;
  font-weight: bold;
}
</style>
