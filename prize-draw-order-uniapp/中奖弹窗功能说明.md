# 中奖弹窗功能说明

## 功能概述

为uniapp抽奖系统新增了精美的中奖弹窗功能，当用户抽奖中奖后，会显示一个带有动画效果的弹窗，提升用户体验。

## 主要特性

### 1. 自动关闭
- 弹窗显示后5秒自动关闭
- 显示倒计时提示用户

### 2. 手动关闭
- 点击右上角关闭按钮
- 点击遮罩层
- 点击操作按钮后关闭

### 3. 精美动画
- 弹窗出现动画
- 烟花动画效果
- 中奖图标跳动动画
- 渐变背景色

### 4. 完整功能
- 显示中奖信息
- 显示奖品名称和描述
- 显示抽奖时间
- 提供领取按钮

## 文件结构

```
components/
└── WinningModal/
    └── WinningModal.vue    # 中奖弹窗组件

pages/
├── lottery/
│   └── lottery.vue         # 抽奖页面（已修改）
└── test-modal/
    └── test-modal.vue      # 测试页面（新增）
```

## 修改内容

### 1. 新增组件
- `components/WinningModal/WinningModal.vue` - 中奖弹窗组件

### 2. 修改抽奖页面
- `pages/lottery/lottery.vue` - 集成中奖弹窗
- 替换原有的 `uni.showModal` 为自定义弹窗
- 添加弹窗相关的数据和方法

### 3. 新增测试页面
- `pages/test-modal/test-modal.vue` - 用于测试弹窗效果

### 4. 更新配置
- `pages.json` - 添加测试页面路由
- `README.md` - 更新文档说明

## 使用方法

### 在抽奖页面中的使用

1. **导入组件**
```javascript
import WinningModal from '@/components/WinningModal/WinningModal.vue'
```

2. **注册组件**
```javascript
components: {
  WinningModal
}
```

3. **添加数据属性**
```javascript
data() {
  return {
    showWinningModal: false,
    currentWinningResult: null
  }
}
```

4. **在模板中使用**
```vue
<WinningModal 
  :visible="showWinningModal" 
  :result="currentWinningResult"
  :autoClose="true"
  :autoCloseDelay="5000"
  @close="handleWinningModalClose"
  @claim="handleClaimPrize"
/>
```

5. **添加处理方法**
```javascript
methods: {
  // 显示中奖弹窗
  showResult(result) {
    if (result.isWinner === '1') {
      this.currentWinningResult = result
      this.showWinningModal = true
    }
  },
  
  // 关闭弹窗
  handleWinningModalClose() {
    this.showWinningModal = false
    this.currentWinningResult = null
  },
  
  // 领取奖品
  handleClaimPrize(result) {
    if (result && result.recordId) {
      uni.navigateTo({
        url: `/pages/claim/claim?recordId=${result.recordId}`
      })
    }
  }
}
```

## 组件API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | Boolean | false | 是否显示弹窗 |
| result | Object | {} | 中奖结果对象 |
| autoClose | Boolean | true | 是否自动关闭 |
| autoCloseDelay | Number | 5000 | 自动关闭延迟时间(ms) |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| close | 弹窗关闭时触发 | - |
| claim | 点击领取按钮时触发 | result |

### result 对象结构

```javascript
{
  isWinner: '1',              // 是否中奖 '1'-中奖 '0'-未中奖
  prizeName: '精美礼品一份',   // 奖品名称
  prizeDesc: '奖品描述',      // 奖品描述（可选）
  drawTime: '2024-01-01T12:00:00Z', // 抽奖时间
  recordId: 'record_001',     // 记录ID
  claimStatus: '0'            // 领取状态 '0'-未领取 '1'-已领取
}
```

## 测试方法

1. **访问测试页面**
   - 在浏览器中访问 `/pages/test-modal/test-modal`
   - 或在HBuilderX中运行项目后导航到测试页面

2. **测试功能**
   - 点击"测试中奖弹窗"按钮测试中奖效果
   - 点击"测试未中奖弹窗"按钮测试未中奖效果

3. **验证功能**
   - 验证弹窗显示效果
   - 验证自动关闭功能
   - 验证手动关闭功能
   - 验证领取按钮功能

## 注意事项

1. **兼容性**
   - 组件使用标准的Vue语法，兼容uniapp各平台
   - 动画效果在不同平台可能有细微差异

2. **性能**
   - 弹窗组件使用了定时器，组件销毁时会自动清理
   - 建议在页面卸载时确保弹窗已关闭

3. **样式**
   - 弹窗使用固定定位，z-index为9999
   - 可根据项目需要调整样式和动画效果

4. **扩展**
   - 可以根据需要添加更多动画效果
   - 可以自定义弹窗内容和样式
   - 可以添加音效等增强体验
