# API权限配置验证

## 修改内容

已为所有uniapp使用的API接口添加了`@Anonymous`注解，使其无需权限验证即可访问：

### 修改的控制器

1. **ApiMerchantController** - 商家API接口
   - 路径：`/api/merchant/**`
   - 添加了 `@Anonymous` 注解

2. **ApiConfigController** - 配置API接口
   - 路径：`/api/config/**`
   - 添加了 `@Anonymous` 注解

3. **ApiLotteryController** - 抽奖API接口
   - 路径：`/api/lottery/**`
   - 添加了 `@Anonymous` 注解

4. **ApiTableController** - 桌台API接口
   - 路径：`/api/table/**`
   - 添加了 `@Anonymous` 注解

## 工作原理

1. `@Anonymous` 注解标记的控制器或方法会被 `PermitAllUrlProperties` 自动扫描
2. 扫描到的URL会被添加到允许匿名访问的URL列表中
3. Spring Security配置会自动将这些URL设置为 `permitAll()`
4. uniapp访问这些接口时不需要提供任何认证信息

## 涉及的API接口

### 商家相关 (/api/merchant)
- GET `/api/merchant/info/{merchantCode}` - 获取商家信息
- GET `/api/merchant/config/{merchantCode}` - 获取商家配置
- GET `/api/merchant/config/{merchantCode}/{configKey}` - 获取特定配置值

### 配置相关 (/api/config)
- GET `/api/config/{merchantCode}` - 获取所有配置
- GET `/api/config/claim-instruction/{merchantCode}` - 获取领取说明
- GET `/api/config/scan-bg/{merchantCode}` - 获取扫码页面背景
- GET `/api/config/lottery-bg/{merchantCode}` - 获取抽奖页面背景
- GET `/api/config/{merchantCode}/{configKey}` - 获取配置值

### 桌台相关 (/api/table)
- GET `/api/table/{merchantCode}/{tableNumber}` - 获取桌台信息
- GET `/api/table/list/{merchantCode}` - 获取桌台列表
- GET `/api/table/info/{tableId}` - 根据ID获取桌台信息

### 抽奖相关 (/api/lottery)
- GET `/api/lottery/activity/{merchantCode}` - 获取当前活动
- GET `/api/lottery/activities/{merchantCode}` - 获取所有有效活动
- GET `/api/lottery/check/{activityId}/{userOpenid}` - 检查参与资格
- GET `/api/lottery/remaining/{activityId}/{userOpenid}` - 获取剩余次数
- POST `/api/lottery/draw` - 执行抽奖
- GET `/api/lottery/records/{userOpenid}` - 获取用户记录
- GET `/api/lottery/records/{activityId}/{userOpenid}` - 获取活动记录
- GET `/api/lottery/winning/{userOpenid}` - 获取中奖记录
- GET `/api/lottery/unclaimed/{userOpenid}` - 获取未领取记录
- GET `/api/lottery/count/today/{activityId}/{userOpenid}` - 今日抽奖次数
- GET `/api/lottery/count/total/{activityId}/{userOpenid}` - 总抽奖次数
- GET `/api/lottery/info/{merchantCode}/{tableNumber}` - 获取抽奖信息

## 验证方法

1. 启动后端服务
2. 使用uniapp或直接HTTP请求访问上述API接口
3. 确认不需要提供Authorization头部即可正常访问
4. 检查返回的数据格式是否正确

## 注意事项

- 这些API接口虽然不需要权限验证，但仍然会进行业务逻辑验证（如商家状态检查）
- 建议在生产环境中考虑添加其他安全措施，如IP白名单、请求频率限制等
- 所有API接口仍然遵循原有的业务规则和数据验证
