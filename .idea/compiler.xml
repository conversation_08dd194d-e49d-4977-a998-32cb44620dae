<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="ruoyi-common" />
        <module name="ruoyi-framework" />
        <module name="ruoyi-generator" />
        <module name="ruoyi-system" />
        <module name="ruoyi-quartz" />
        <module name="ruoyi-admin" />
      </profile>
    </annotationProcessing>
  </component>
</project>